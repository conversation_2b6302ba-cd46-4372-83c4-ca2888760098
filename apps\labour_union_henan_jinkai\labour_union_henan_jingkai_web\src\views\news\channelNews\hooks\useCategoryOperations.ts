import { ref, unref } from 'vue';
import { divide, toNumber, map, find } from 'lodash-es';
import { getMaxSortNumberByCategoryId } from '@/api/news';
import { useMessage } from '@monorepo-yysz/hooks';

/**
 * 栏目操作管理 Hook
 */
export function useCategoryOperations() {
  const { createMessage } = useMessage();

  /**
   * 处理栏目切换通用逻辑
   */
  const handleCategoryChange = (
    platformType: string,
    categoryInfo: Recordable,
    dictionary: any,
    tabsPane: any,
    detailList: any,
    activeTabs: any,
    category: any,
    appCoverWhether: any,
    appMaxCoverNumber: any,
    gwCoverWhether: any,
    gwMaxCoverNumber: any,
    categorySortOrder: any,
    imageSize: any,
    imageSizeName: any,
    isUpdate: any,
    setFieldsValue: Function
  ) => {
    category.value = categoryInfo;
    appCoverWhether.value = categoryInfo.appCoverWhether;
    appMaxCoverNumber.value = categoryInfo.appMaxCoverNumber;
    gwCoverWhether.value = categoryInfo.gwCoverWhether;
    gwMaxCoverNumber.value = categoryInfo.gwMaxCoverNumber;
    categorySortOrder.value = categoryInfo.categorySortOrder;

    const typeArr = platformType.split(',') || [];

    // 生成Tab面板数据
    tabsPane.value = map(typeArr, v => {
      const node = find(unref(detailList), f => f.platformType === v);
      const categoryPlatformType = categoryInfo.platformType;
      let abstractWhether = '';

      if (categoryPlatformType.includes(v) && 'GHAPP' === v) {
        abstractWhether = categoryInfo.appAbstractWhether;
      } else if (categoryPlatformType.includes(v) && 'GHGW' === v) {
        abstractWhether = categoryInfo.gwAbstractWhether;
      }

      if (node) {
        const { newsCoverUrl } = node;
        const split = {};
        map(newsCoverUrl?.split(',') || [], (a, b) => (split[`newsCoverUrl${b + 1}`] = a));

        return {
          ...node,
          ...split,
          title: dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName,
          platformType: v + '',
          key: `appType_${v}`,
          abstractWhether,
          // whetherLinkResources: node.whetherLinkResources || false,
          // detailsWhetherPrompt: node.detailsWhetherPrompt || false,
        } as Recordable;
      }

      return {
        title: dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName,
        platformType: v + '',
        key: `appType_${v}`,
        abstractWhether,
        // whetherLinkResources: node.whetherLinkResources || false,
        // detailsWhetherPrompt: node.detailsWhetherPrompt || false,
      } as Recordable;
    });

    // 设置活动Tab
    activeTabs.value = unref(tabsPane)[0].platformType;

    // 处理裁剪比例
    handleImageCropping(categoryInfo, activeTabs, imageSize, imageSizeName);

    // 处理排序号
    handleSortNumber(categorySortOrder, isUpdate, categoryInfo, setFieldsValue);
  };

  /**
   * 处理图片裁剪比例
   */
  const handleImageCropping = (
    categoryInfo: Recordable,
    activeTabs: any,
    imageSize: any,
    imageSizeName: any
  ) => {
    const { cuttingRatioStart, cuttingRatioEnd, gwCuttingRatioStart, gwCuttingRatioEnd } =
      categoryInfo;

    if ('GHAPP' === unref(activeTabs)) {
      imageSizeName.value = `${cuttingRatioStart} * ${cuttingRatioEnd}`;
      const cuttingFlg = cuttingRatioStart && cuttingRatioEnd;
      imageSize.value = cuttingFlg
        ? toNumber(divide(cuttingRatioStart, cuttingRatioEnd).toFixed(2))
        : 1;
    } else if ('GHGW' === unref(activeTabs)) {
      imageSizeName.value = `${gwCuttingRatioStart} * ${gwCuttingRatioEnd}`;
      const cuttingFlg = gwCuttingRatioStart && gwCuttingRatioEnd;
      imageSize.value = cuttingFlg
        ? toNumber(divide(gwCuttingRatioStart, gwCuttingRatioEnd).toFixed(2))
        : 1;
    }
  };

  /**
   * 处理排序号设置
   */
  const handleSortNumber = async (
    categorySortOrder: any,
    isUpdate: any,
    categoryInfo: Recordable,
    setFieldsValue: Function
  ) => {
    if ('SORT_NUMBER' === unref(categorySortOrder) && !unref(isUpdate)) {
      try {
        const res = await getMaxSortNumberByCategoryId({ categoryId: categoryInfo.categoryId });
        const { code, message: msg, data } = res;
        if (code === 200) {
          setFieldsValue({ sortNumber: data });
        } else {
          createMessage.error(`最大排序号获取失败!${msg}`);
        }
      } catch (error) {
        console.error('获取最大排序号失败:', error);
      }
    } else {
      setFieldsValue({ sortNumber: undefined });
    }
  };

  /**
   * 处理Tab切换
   */
  const handleTabChange = (
    key: string,
    category: any,
    activeTabs: any,
    imageSize: any,
    imageSizeName: any
  ) => {
    activeTabs.value = key;
    const { cuttingRatioStart, cuttingRatioEnd, gwCuttingRatioStart, gwCuttingRatioEnd } = unref(
      category
    ) as Recordable;

    if ('GHAPP' === key) {
      imageSizeName.value = `${cuttingRatioStart} * ${cuttingRatioEnd}`;
      const cuttingFlg = cuttingRatioStart && cuttingRatioEnd;
      imageSize.value = cuttingFlg
        ? toNumber(divide(cuttingRatioStart, cuttingRatioEnd).toFixed(2))
        : 1;
    } else if ('GHGW' === key) {
      imageSizeName.value = `${gwCuttingRatioStart} * ${gwCuttingRatioEnd}`;
      const cuttingFlg = gwCuttingRatioStart && gwCuttingRatioEnd;
      imageSize.value = cuttingFlg
        ? toNumber(divide(gwCuttingRatioStart, gwCuttingRatioEnd).toFixed(2))
        : 1;
    }
  };

  return {
    handleCategoryChange,
    handleImageCropping,
    handleSortNumber,
    handleTabChange,
  };
}
