<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ExchangeModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import ExchangeModal from './ExchangeModal.vue';
import { list, view } from '@/api/productManagement/integralExchangeRecord';

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

// 详情
function handleView(record: Recordable<any>) {
  view({ productId: record.productId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}
</script>
