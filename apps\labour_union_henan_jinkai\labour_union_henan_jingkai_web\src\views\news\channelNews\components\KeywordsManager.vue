<template>
  <div class="keywords-manager">
    <!-- 提取关键词按钮 -->
    <a-row
      v-if="platformType === 'GHAPP' && hasContent"
      class="m-20px !mt-0"
    >
      <a-col :span="24">
        <a-button
          @click="handleExtractKeywords"
          type="primary"
          :loading="keywordState.isExtracting"
        >
          {{ keywordState.isExtracting ? '提取中...' : '提取关键词' }}
        </a-button>
      </a-col>
    </a-row>

    <!-- 候选关键词选择 -->
    <a-row
      v-if="platformType === 'GHAPP' && candidateKeywords.length > 0"
      class="m-20px !mt-0"
    >
      <a-col
        :span="2"
        class="justify-center items-center !flex"
      >
        <label>候选关键词：</label>
      </a-col>
      <a-col :span="22">
        <a-checkbox-group
          :options="candidateKeywords"
          @change="handleCandidateChange"
          :value="selectedKeywords"
          :disabled="keywordState.tags.length >= KEYWORD_LIMITS.MAX_COUNT"
        />
      </a-col>
    </a-row>

    <!-- 关键词提示 -->
    <a-row
      v-if="platformType === 'GHAPP'"
      class="m-20px !mt-0"
    >
      <a-col
        :span="24"
        class="items-center !flex"
      >
        <label>
          <span class="!ml-5px text-red-600">
            {{ getKeywordTip() }}
          </span>
        </label>
      </a-col>
    </a-row>

    <!-- 关键词标签管理 -->
    <a-row
      v-if="platformType === 'GHAPP'"
      class="m-20px !mt-0"
    >
      <a-col
        :span="3"
        class="justify-center items-center !flex"
      >
        <label>资讯关键词：</label>
      </a-col>
      <a-col :span="21">
        <!-- 关键词标签 -->
        <template
          v-for="tag in keywordState.tags"
          :key="tag"
        >
          <a-tooltip
            v-if="tag.length > 20"
            :title="tag"
          >
            <a-tag
              color="orange"
              :closable="true"
              @close="handleRemoveKeyword(tag)"
            >
              {{ `${tag.slice(0, 20)}...` }}
            </a-tag>
          </a-tooltip>
          <a-tag
            v-else
            color="orange"
            :closable="true"
            @close="handleRemoveKeyword(tag)"
          >
            {{ tag }}
          </a-tag>
        </template>

        <!-- 输入框 -->
        <a-input
          v-if="keywordState.inputVisible"
          ref="inputRef"
          v-model:value="keywordState.inputValue"
          type="text"
          size="small"
          :style="{ width: '140px' }"
          :maxlength="KEYWORD_LIMITS.MAX_LENGTH"
          @blur="handleConfirmInput"
          @keyup.enter="handleConfirmInput"
          @keyup.esc="handleCancelInput"
        />

        <!-- 添加按钮 -->
        <a-tag
          v-else
          color="orange"
          style="background: #fff; border-style: dashed"
          @click="handleShowInput"
          :style="{
            cursor:
              keywordState.tags.length >= KEYWORD_LIMITS.MAX_COUNT ? 'not-allowed' : 'pointer',
            opacity: keywordState.tags.length >= KEYWORD_LIMITS.MAX_COUNT ? 0.6 : 1,
          }"
        >
          <PlusOutlined />
          新增关键词
        </a-tag>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useKeywordsManager } from './hooks/useKeywordsManager';

interface Props {
  platformType?: string;
  content?: string;
  record?: any;
  isUpdate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  platformType: '',
  content: '',
  isUpdate: false,
});

const emit = defineEmits<{
  getTags: [tags: string[]];
}>();

// 使用关键词管理 Hook
const {
  candidateKeywords,
  selectedKeywords,
  keywordState,
  inputRef,
  KEYWORD_LIMITS,
  extractKeywords,
  handleCandidateKeywordsChange,
  removeKeyword,
  showKeywordInput,
  confirmKeywordInput,
  cancelKeywordInput,
  getKeywordTip,
} = useKeywordsManager(props, emit);

// 计算属性
const hasContent = computed(() => !!props.content?.trim());

// 事件处理
const handleExtractKeywords = () => {
  extractKeywords({ newsDetailsContent: props.content });
};

const handleCandidateChange = (selectedList: string[]) => {
  handleCandidateKeywordsChange(selectedList);
};

const handleRemoveKeyword = (tag: string) => {
  removeKeyword(tag);
};

const handleShowInput = () => {
  showKeywordInput();
};

const handleConfirmInput = () => {
  confirmKeywordInput();
};

const handleCancelInput = () => {
  cancelKeywordInput();
};
</script>

<style scoped>
.keywords-manager .ant-tag {
  margin-bottom: 8px;
}

.keywords-manager .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keywords-manager .ant-checkbox-group .ant-checkbox-wrapper {
  margin-right: 0;
}
</style>
