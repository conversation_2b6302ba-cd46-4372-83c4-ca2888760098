<template>
  <div :class="$style['activity-info']">
    <Row class="divide-x-reverse max-h-72vh overflow-y-auto">
      <Col :span="5">
        <Collapse
          v-model:activeKey="collapseActiveKey"
          @change="collapseChange"
          v-if="collapsePanelArr.length > 0"
        >
          <Panel
            :key="item.key"
            :header="item.header"
            :img-size-text="item.imageSizeText"
            :img-size="item.imageSize"
            :value="item.cover"
            :class="`${item.class}`"
            :readOnly="disabled"
            :errorImg="item.errorImg"
            :operate-type="activityType ? ActivityDocAddr[activityType] : undefined"
            @change="({ val }) => (item.cover = val)"
            @delete="() => (item.cover = item.origin)"
            v-for="item in collapseArr"
          />
        </Collapse>
        <div
          v-else
          class="h-full"
        >
          <Empty
            description="封面图展示"
            class="justify-center h-full w-full"
          />
        </div>
      </Col>
      <Col :span="19">
        <BasicForm
          @register="registerForm"
          :class="`basic-form ${disabledClass}`"
        >
          <template
            #[item]="data"
            v-for="item in Object.keys($slots)"
          >
            <slot
              :name="item"
              v-bind="data || {}"
            ></slot>
          </template>
          <template #publishPort="{ model, field }">
            <CheckboxGroup
              v-model:value="model[field]"
              :disabled="disabled"
              :options="appTypeComputedOpt"
              @change="handlePublishPort"
            />
          </template>
        </BasicForm>
      </Col>
    </Row>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch, nextTick, inject } from 'vue';
import { CheckboxGroup, Col, Collapse, Empty, Row } from 'ant-design-vue';
import { BasicForm, useForm } from '@/components/Form';
import { getConfigLabel, modalFormSchema } from './data';
import { InfoActionProps, InfoActionType } from './useInfo';
import { ActivityDocAddr, ActivityType } from '../../activities.d';
import { useDictionary } from '@/store/modules/dictionary';
import Panel from './components/Panel.vue';
import { find, isEmpty } from 'lodash-es';
import phoneUpload from '@/assets/images/phoneUpload.png';
import uploadBg from '@/assets/images/uploadBg.png';
import pcList from '@/assets/images/pcList.png';
import pcInfo from '@/assets/images/pcInfo.png';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import type { CheckboxValueType } from 'ant-design-vue/lib/checkbox/interface';
import type { Key } from 'ant-design-vue/lib/_util/type';

// 活动类型对应的图片尺寸配置
const ACTIVITY_IMAGE_CONFIG = {
  [ActivityType.SUMMER_COOLNESS]: {
    imageSize: 280 / 220,
    imageSizeText: '280*220',
  },
  [ActivityType.INCLUSIVE_OTHER]: {
    imageSize: 335 / 164,
    imageSizeText: '335*164',
  },
  default: {
    imageSize: 690 / 260,
    imageSizeText: '690*260',
  },
};

const emit = defineEmits(['register']);

const dictionary = useDictionary();
const appType = dictionary.getDictionaryOpt.get('appType');

const bigActivityType = inject<string | undefined>('bigActivityType', undefined);

// 响应式状态
const disabled = ref<boolean>(false);
const collapsePanelArr = ref<string[]>(['30']);
const collapseActiveKey = ref(['app-info-bg', 'app-list-bg', 'pc-list-bg', 'pc-info-bg']);
const activityType = ref<ActivityType>();

// PC端背景图配置
const pcBgConfig = [
  {
    key: 'pc-list-bg',
    header: 'Pc-列表页封面图',
    type: '20',
    imageSize: 380 / 275,
    imageSizeText: '380*275',
    errorImg: pcList,
    imgKey: 'pcCover',
    class: 'w-332px h-240px',
  },
  {
    key: 'pc-info-bg',
    header: 'Pc-详情页背景图',
    type: '20',
    imageSize: 1180 / 265,
    imageSizeText: '1180*265',
    errorImg: pcInfo,
    imgKey: 'pcDetailsCover',
    class: 'w-332px h-78px',
  },
];

// APP端背景图配置（基础配置）
const appBgBaseConfig = [
  {
    key: 'app-info-bg',
    header: 'App-列表页封面图',
    type: '30',
    errorImg: uploadBg,
    imgKey: 'appCover',
    class: 'w-332px h-170px',
  },
  {
    key: 'app-list-bg',
    header: 'App-详情页背景图',
    type: '30',
    imageSize: 1080 / 2040,
    imageSizeText: '1080*2040',
    errorImg: phoneUpload,
    imgKey: 'appDetailsCover',
    class: 'w-332px h-585px',
  },
];

// 生成初始APP背景图配置的函数
function generateAppBgConfig(currentActivityType?: ActivityType) {
  const config = currentActivityType
    ? ACTIVITY_IMAGE_CONFIG[currentActivityType] || ACTIVITY_IMAGE_CONFIG.default
    : ACTIVITY_IMAGE_CONFIG.default;

  return appBgBaseConfig.map((item, index) => ({
    ...item,
    cover: '',
    origin: '',
    // 只对第一个元素应用动态尺寸配置
    ...(index === 0 ? config : {}),
  }));
}

// APP背景图数据（改为ref以支持响应式更新）
const appBg = ref(generateAppBgConfig());

// PC背景图数据
const pcBg = ref(
  pcBgConfig.map(item => ({
    ...item,
    cover: '',
    origin: '',
  }))
);

// 折叠面板数据 - 修复异步渲染问题
const collapseArr = computed(() => {
  const currentActivityType = unref(activityType);
  const currentAppBg = unref(appBg);

  if (currentActivityType === ActivityType.QUIZ) {
    return [...currentAppBg];
  }
  return currentAppBg.length > 0 ? [currentAppBg[0]] : [];
});

const formItem = computed(() => {
  const currentActivityType = unref(activityType);
  if (isEmpty(currentActivityType)) {
    return [];
  }

  return modalFormSchema(
    currentActivityType || ActivityType.QUIZ,
    {
      ifQuizLabel: getConfigLabel(currentActivityType!),
    },
    bigActivityType
  );
});

const appTypeComputedOpt = computed(() => {
  return dictionary.getDictionaryOpt.get('appType') as RadioGroupChildOption[];
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const [
  registerForm,
  { validate, resetFields, setProps: setPropsFrom, setFieldsValue, resetDefaultField },
] = useForm({
  labelWidth: 140,
  schemas: formItem,
  showActionButtonGroup: false,
  baseRowStyle: {
    padding: '10px',
    border: '1px solid #e6e6e6',
  },
});

//注册hooks
async function validateInfo() {
  const values = await validate();
  const publishPort = values.publishPort;
  const cover =
    publishPort?.length === appType?.length
      ? {
          appCover: getImgCover('appCover'),
          appDetailsCover: getImgCover('appDetailsCover'),
          pcCover: getImgCover('pcCover'),
          pcDetailsCover: getImgCover('pcDetailsCover'),
        }
      : publishPort && publishPort.includes('20')
        ? { pcCover: getImgCover('pcCover'), pcDetailsCover: getImgCover('pcDetailsCover') }
        : {
            appCover: getImgCover('appCover'),
            appDetailsCover: getImgCover('appDetailsCover'),
          };

  const params = {
    ...values,
    ...cover,
    publishPort: publishPort ? publishPort.join(',') : undefined,
    startTime: values.dateTime && values.dateTime.length > 0 ? values.dateTime[0] : undefined,
    endTime: values.dateTime && values.dateTime.length > 0 ? values.dateTime[1] : undefined,
  };

  return params;
}

// 重置表单
async function reset() {
  await resetFields();

  // 重置图片数据
  const currentActivityType = unref(activityType);
  appBg.value = generateAppBgConfig(currentActivityType);

  pcBg.value = pcBgConfig.map(item => ({
    ...item,
    cover: '',
    origin: '',
  }));

  collapsePanelArr.value = ['30'];
  collapseActiveKey.value = ['app-info-bg', 'app-list-bg', 'pc-list-bg', 'pc-info-bg'];
}

// 设置组件属性
async function setProps({ disabled: flg, activityType: aType }: InfoActionProps) {
  const prevActivityType = unref(activityType);

  // 设置新的活动类型
  activityType.value = aType;
  setPropsFrom({ disabled: flg });
  disabled.value = !!flg;

  // 如果活动类型发生变化，等待响应式更新完成
  if (prevActivityType !== aType) {
    await nextTick();
    // 确保表单 schema 更新完成
    await new Promise(resolve => setTimeout(resolve, 50));
  }
}

// 获取图片封面
function getImgCover(key: string): string {
  const node = find(unref(collapseArr), (v: Recordable) => v.imgKey === key) as Recordable;
  if (node) {
    const { cover } = node;
    return cover && cover.includes('/src/assets') ? '' : cover;
  }
  return '';
}

// 设置表单值并更新图片
async function setValues(values: Recordable,flag?:boolean) {
  if(flag){
    await setFieldsValue({
      ...values,
    });
    return
  }
  const { publishPort, startTime, endTime } = values;

  // 标记正在设置值，避免watch干扰
  isSettingValues.value = true;

  try {
    // 确保 formItem 已经准备好（等待 activityType 设置完成）
    await nextTick();

    // 等待表单 schema 更新并且表单字段渲染完成
    let retryCount = 0;
    while (unref(formItem).length === 0 && retryCount < 10) {
      await new Promise(resolve => setTimeout(resolve, 50));
      retryCount++;
    }

    await setFieldsValue({
      ...values,
      publishPort: publishPort?.split(',') || [],
      dateTime: startTime && endTime ? [startTime, endTime] : undefined,
    });

    // 等待下一个tick确保DOM更新
    await nextTick();

    // 重新生成配置以确保使用最新的activityType
    const currentActivityType = unref(activityType);
    appBg.value = generateAppBgConfig(currentActivityType);

    // 设置新的图片数据 - 直接修改appBg的ref值
    appBg.value.forEach((item: any) => {
      const imgCover = values[item.imgKey];
      if (imgCover) {
        item.cover = imgCover;
        item.origin = imgCover;
      }
    });

    // 重新生成PC背景图配置并设置数据
    pcBg.value = pcBgConfig.map(item => ({
      ...item,
      cover: values[item.imgKey] || '',
      origin: values[item.imgKey] || '',
    }));

    collapsePanelArr.value = publishPort ? publishPort.split(',') : [];

    // 再次等待确保所有响应式更新完成
    await nextTick();
  } finally {
    // 确保标记被重置
    isSettingValues.value = false;
  }
}

const infoActionType: Partial<InfoActionType> = {
  validate: validateInfo,
  reset: reset,
  setProps: setProps as any,
  setValues: setValues as any,
  resetDefaultField: async (nameList?: any) => {
    resetDefaultField(nameList);
  },
};

onMounted(() => {
  emit('register', infoActionType);
});

//展开图片
function collapseChange(change: Key | Key[]) {
  const changeArray = Array.isArray(change) ? change : [change];
  collapseActiveKey.value = changeArray.map(String);
}

//多端类型
function handlePublishPort(checkedValue: CheckboxValueType[]) {
  collapsePanelArr.value = checkedValue.map(String);
}

// 标记是否正在设置值，避免watch干扰
const isSettingValues = ref(false);

// 监听 activityType 变化，更新 appBg 配置
watch(
  activityType,
  async newActivityType => {
    // 如果正在设置值，跳过这次更新
    if (isSettingValues.value || !newActivityType) {
      return;
    }

    // 保存当前的cover和origin值
    const currentCovers = appBg.value.map(item => ({
      imgKey: item.imgKey,
      cover: item.cover,
      origin: item.origin,
    }));

    // 重新生成配置
    appBg.value = generateAppBgConfig(newActivityType);

    // 恢复之前的cover和origin值（只在有值的情况下）
    appBg.value.forEach(item => {
      const saved = currentCovers.find(c => c.imgKey === item.imgKey);
      if (saved && saved.cover) {
        item.cover = saved.cover;
        item.origin = saved.origin;
      }
    });

    await nextTick();
  },
  { immediate: false }
);
</script>

<style lang="less" module>
.activity-info {
  :global {
    .ant-picker-range {
      width: 75% !important;
    }
  }
}
</style>
