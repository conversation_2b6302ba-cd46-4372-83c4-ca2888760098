<template>
  <div>
    <PageLoadingWrapper
      :loading="pageLoading"
      :has-error="hasError"
      :error-description="errorDescription"
      :error-detail="`栏目ID: ${categoryId || '未知'}`"
      loading-tip="正在加载栏目信息..."
      loading-title="初始化页面中"
      loading-description="正在验证栏目信息和权限..."
      back-button-text="返回首页"
      retry-button-text="重新加载"
      :custom-retry-handler="handleReload"
      @back="handleGoBack"
      @retry="handleRetry"
    >
      <basic-table @register="registerTable">
        <template #toolbar>
          <a-button
            type="primary"
            @click="handleClick"
            :auth="`${menuPath}/add`"
          >
            {{ `新增${categoryInfos?.categoryName}新闻` || '新增新闻' }}
          </a-button>
        </template>
        <template #form-categoryId="{ model, field }">
          <TreeSelect
            v-model:value="model[field]"
            placeholder="请选择新闻栏目"
            :showSearch="true"
            :filterOption="filterOption"
            :fieldNames="{ label: 'categoryName', value: 'categoryId', children: 'children' }"
            :treeData="categoryOptions"
          />
        </template>
        <template #form-queryCompanyId="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择所属工会"
            :showSearch="true"
            :filterOption="queryCompanyIdFilterOption"
            :fieldNames="{ label: 'companyName', value: 'companyId' }"
            :options="queryCompanyOptions"
          />
        </template>
        <template #form-nextLevelFlag="{ model, field }">
          <Checkbox
            v-model:checked="model[field]"
            @change="changeNextLevelFlag"
          />
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="tableActions(record)" />
          </template>
        </template>
      </basic-table>
      <ChanelNewsModal
        @register="registerModal"
        :can-fullscreen="false"
        width="88%"
        @success="handleSuccess"
        @audit="handleSuccess"
        @view="handleView"
        :addTitle="`新增${categoryInfos?.categoryName}新闻` || '新增新闻'"
        :okButtonAuth="`${menuPath}/timelyPublish`"
        :otherButtonAuth="`${menuPath}/submitAudit`"
      />

      <ViewModal
        @register="registerView"
        :can-fullscreen="false"
        width="40%"
      />
      <!-- 播报预览 -->
      <BroadcastModel
        @register="broadcastView"
        :can-fullscreen="false"
        width="40%"
      />
      <!-- 播报管理 -->
      <BroadcastManageModel
        @register="broadcastManageView"
        :can-fullscreen="false"
        width="40%"
      />
      <TopModal
        @register="registerTop"
        :can-fullscreen="false"
        width="40%"
        @success="handleTopSuccess"
      />
      <SortModel
        @register="registerSort"
        :can-fullscreen="false"
        width="40%"
        @success="handleSortSuccess"
      />

      <!-- 统计指标明细 -->
      <StatisticalDetailsModel
        @register="registerStatisticalDetails"
        :can-fullscreen="false"
        width="60%"
      />

      <!-- 评论明细 -->
      <ReviewDetailsModel
        @register="registerReviewDetails"
        :can-fullscreen="false"
        width="60%"
      />
      <!--  发布按钮弹窗-->
      <BasicModal
        v-model:open="visible"
        :title="title"
        @ok="handleOk"
        :can-fullscreen="false"
        :bodyStyle="{ height: '40vh' }"
      >
        <Form
          :model="formState"
          :labelCol="{ span: 4 }"
          :wrapperCol="{ span: 14 }"
        >
          <FormItem
            label="发布时间"
            class="!mt-2"
          >
            <DatePicker
              v-model:value="formState.publishTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm"
              showTime
            />
          </FormItem>
        </Form>
      </BasicModal>
      <!--  列表点击审核状态,审核记录弹窗  -->
      <BasicModal
        v-model:open="auditVisible"
        @cancel="handleCancel"
        width="40%"
        :title="`${line?.newsTitle || ''}审核记录`"
        :can-fullscreen="false"
        @ok="handleCancel"
      >
        <div class="p-5 h-full overflow-y-auto">
          <Timeline>
            <TimelineItem
              v-for="item in auditLines"
              :color="item.newsAuditStatus === 'pass' ? 'green' : 'red'"
            >
              <div>审核时间：{{ item.createTime }}</div>
              <div>审核人：{{ item.createUser }}</div>
              <div>审核状态：{{ item.newsAuditStatus === 'pass' ? '通过' : '拒绝' }}</div>
              <div v-if="item.newsAuditStatus === 'refuse'"
                >拒绝原因：{{
                  dictionary.getDictionaryMap.get(`newsRejectCategory_${item.newsRejectCategory}`)
                    ?.dictName || '无'
                }}
              </div>
              <div>审核意见：{{ item.newsAuditInstruction || '无' }}</div>
            </TimelineItem>
          </Timeline>
        </div>
        <template #footer> </template>
      </BasicModal>
    </PageLoadingWrapper>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { authColumn, columns, formSchemas, recordColumn } from '@/views/news/channelNews/data';
import {
  getNewsList,
  newsAdd,
  NewsAuditRecord,
  newsDelete,
  newsGetOneNews,
  newsRelease,
  newsTop,
  newsUpdate,
  getNewsMainTable,
  setNewsSort,
  getCategoryInfoByCategoryId,
} from '@/api/news';
import { BasicModal, useModal } from '@/components/Modal';
import { PageLoadingWrapper, usePageStatus } from '@/components/PageStatus';
import ChanelNewsModal from '@/views/news/channelNews/ChanelNewsModal.vue';
import ViewModal from '@/views/news/channelNews/ViewModal.vue';
import { Form, DatePicker, Timeline, TimelineItem } from 'ant-design-vue';
import { createVNode, ref, unref, computed, watch, onMounted, provide } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import TopModal from '@/views/news/channelNews/TopModal.vue';
import SortModel from '@/views/news/channelNews/SortModel.vue';
import StatisticalDetailsModel from '@/views/news/channelNews/StatisticalDetailsModel.vue';
import { filter, find } from 'lodash-es';
import { useInfos } from '@/store/modules/infos';
import { useDictionary } from '@/store/modules/dictionary';
import BroadcastModel from '@/views/news/channelNews/BroadcastModel.vue';
import BroadcastManageModel from '@/views/news/channelNews/BroadcastManageModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { TreeSelect, Checkbox, Select } from 'ant-design-vue';
import { getUnionTree } from '/@/api/category';
import { list as newsReleaseUnionlList } from '@/api/category/newsReleaseUnion';
import ReviewDetailsModel from '@/views/news/channelNews/ReviewDetailsModel.vue';
import { useRouter } from 'vue-router';
import { PageEnum } from '@/enums/pageEnum';

// interface Props {
//   /** 权限配置对象 */
//   authConfig?: {
//     /** 新增权限 */
//     add?: string;
//     /** 查看权限 */
//     view?: string;
//     /** 编辑权限 */
//     modify?: string;
//     /** 发布权限 */
//     publish?: string;
//     /** 撤销权限 */
//     revoke?: string;
//     /** 删除权限 */
//     delete?: string;
//     /** 置顶权限 */
//     top?: string;
//     /** 排序权限 */
//     sort?: string;
//     /** 播报权限 */
//     broadcast?: string;
//     /** 播报管理权限 */
//     broadcastManage?: string;
//     /** 统计详情权限 */
//     statisticalDetails?: string;
//     /** 评论详情权限 */
//     reviewsDetails?: string;
//     /** 详情链接权限 */
//     detailsUrl?: string;
//     /** 一键发布权限 */
//     okButtonAuth?: string;
//     /** 提交审核权限 */
//     otherButtonAuth?: string;
//   };
//   addTitle?: string;
// }

// const props = withDefaults(defineProps<Props>(), {
//   authConfig: () => ({}),
//   addTitle: () => '新增新闻',
// });

const FormItem = Form.Item;

const userStore = useUserStore();

const { currentRoute, push } = useRouter();

// 使用页面状态管理 Hook
const { pageLoading, hasError, errorDescription, initPageData, reloadPageData } = usePageStatus({
  loadingTip: '正在加载栏目信息...',
  loadingTitle: '初始化页面中',
  loadingDescription: '正在验证栏目信息和权限...',
  defaultErrorDescription: '当前栏目不存在或已被删除',
  validateData: data => !!data,
  customBackHandler: () => push(PageEnum.BASE_HOME_COPY),
});

// 栏目信息直接从 pageData 获取
const infos = useInfos();

//数据字典
const dictionary = useDictionary();

const { createErrorModal, createSuccessModal, createMessage, createConfirm } = useMessage();

const categoryOptions = ref<Recordable[]>([]);

const queryCompanyOptions = ref([]);

const ifcollect = ref(false);

const auditVisible = ref(false);

const auditLines = ref<Recordable[]>([]);

const formState = ref<Recordable>({
  publishTime: '',
});

const categoryInfos = ref<Recordable>();

provide<Recordable>('categoryInfos', categoryInfos);

const title = computed(() => {
  return `发布${unref(formState).newsTitle}`;
});

const line = computed(() => {
  return infos.getRecord as Recordable;
});

const categoryId = computed(() => {
  return unref(currentRoute)?.meta?.categoryIdListStr || '';
});

const menuPath = computed(() => {
  return unref(currentRoute)?.path || '';
});

// 计算属性：动态生成 TableAction 配置
const tableActions = computed(() => {
  return (record: Recordable) => {
    const currentMenuPath = unref(menuPath); // 动态获取最新 menuPath
    return [
      {
        label: '预览',
        icon: 'ant-design:eye-filled',
        type: 'default' as const,
        onClick: handleRecordView.bind(null, record),
        auth: `${currentMenuPath}/view`,
        disabled: record.isUploadFiles,
      },
      {
        icon: 'fa6-solid:pen-to-square',
        label: '编辑',
        type: 'primary' as const,
        onClick: handleEdit.bind(null, record),
        auth: `${currentMenuPath}/modify`,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              ((record.newsPublishStatus === '20' && record.newsAuditStatus === '10') ||
                (record.newsPublishStatus === '-1' && record.newsAuditStatus === '40')),
      },
      {
        icon: 'ic:baseline-published-with-changes',
        label: '发布',
        type: 'primary' as const,
        onClick: handlePublish.bind(null, record, 'release'),
        auth: `${currentMenuPath}/publish`,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              record.newsAuditStatus === '30' &&
              record.newsPublishStatus === '0',
      },
      {
        icon: 'bx:log-out-circle',
        label: '撤销',
        type: 'primary' as const,
        onClick: handlePublish.bind(null, record, 'revoke'),
        auth: `${currentMenuPath}/revoke`,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              (record.newsPublishStatus === '10' || record.newsAuditStatus === '30'),
      },
      {
        icon: 'fluent:delete-20-filled',
        label: '删除',
        type: 'primary' as const,
        danger: true,
        onClick: handleDelete.bind(null, record),
        auth: `${currentMenuPath}/delete`,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              ((record.newsPublishStatus === '20' && record.newsAuditStatus === '10') ||
                (record.newsPublishStatus === '-1' && record.newsAuditStatus === '40')),
      },
      {
        icon: 'carbon:align-vertical-top',
        label: '置顶',
        type: 'primary' as const,
        onClick: handleTop.bind(null, record),
        auth: `${currentMenuPath}/top`,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              'publishAndTop' === record.categorySortOrder,
      },
      {
        icon: 'mdi:sort',
        label: '排序',
        type: 'primary' as const,
        onClick: handleSort.bind(null, record),
        auth: `${currentMenuPath}/sort`,
        disabled: !record.whetherSetSort,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId &&
              'sortNumber' === record.categorySortOrder,
      },
      {
        label: '播报预览',
        icon: 'icon-park-outline:broadcast-radio',
        type: 'primary' as const,
        onClick: handleBroadcast.bind(null, record),
        auth: `${currentMenuPath}/broadcast`,
        ifShow: 'manualEntry' === record.aiBroadcastType,
      },
      {
        label: '生成详情',
        icon: 'material-symbols:manage-search',
        type: 'primary' as const,
        onClick: handleBroadcastManage.bind(null, record),
        auth: `${currentMenuPath}/broadcastManage`,
        ifShow: 'automatically' === record.aiBroadcastType,
      },
      {
        icon: 'material-symbols:summarize-outline-rounded',
        label: '统计',
        type: 'primary' as const,
        onClick: handleStatisticalDetails.bind(null, record),
        auth: `${currentMenuPath}/statisticalDetails`,
      },
      {
        icon: 'material-symbols:summarize-outline-rounded',
        label: '评论',
        type: 'primary' as const,
        onClick: handleReviewDetails.bind(null, record),
        auth: `${currentMenuPath}/reviewsDetails`,
      },
      {
        icon: 'line-md:link',
        label: '频道链接',
        type: 'primary' as const,
        tooltip: record?.detailsUrl,
        ifShow:
          userStore.getUserInfo.accountType === 'unionRoot'
            ? true
            : userStore.getUserInfo.companyId === record.companyId,
        auth: `${currentMenuPath}/detailsUrl`,
      },
    ];
  };
});

const filterOption = (input: string, option: any) => {
  return option.categoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const queryCompanyIdFilterOption = (input: string, option: any) => {
  return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  authInfo: `${unref(menuPath)}/add`,
  columns: columns(false),
  showIndexColumn: false,
  api: getNewsList,
  formConfig: {
    labelWidth: 120,
    schemas: filter(formSchemas(false), v => v.field !== 'categoryId'),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    const { pushDateRange } = params;
    if (pushDateRange && pushDateRange.length === 2) {
      params.publishTimeStart = pushDateRange[0];
      params.publishTimeEnd = pushDateRange[1];
    }
    params.pushDateRange = undefined;
    params.platformType = 30;
    params.categoryId = unref(categoryId);
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      `${unref(menuPath)}/modify`,
      `${unref(menuPath)}/publish`,
      `${unref(menuPath)}/revoke`,
      `${unref(menuPath)}/delete`,
      `${unref(menuPath)}/top`,
      `${unref(menuPath)}/view`,
      `${unref(menuPath)}/sort`,
      `${unref(menuPath)}/broadcast`,
      `${unref(menuPath)}/statisticalDetails`,
      `${unref(menuPath)}/broadcastManage`,
      `${unref(menuPath)}/reviewsDetails`,
      `${unref(menuPath)}/detailsUrl`,
    ],
    width: 470,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerView, { openModal: openView }] = useModal();
//播报预览
const [broadcastView, { openModal: openBroadcast }] = useModal();
//播报管理
const [broadcastManageView, { openModal: openBroadcastManage }] = useModal();

const [registerTop, { openModal: openTop, closeModal: closeTop }] = useModal();

const [registerSort, { openModal: openSort, closeModal: closeSort }] = useModal();

const [registerStatisticalDetails, { openModal: openStatisticalDetails }] = useModal();

const [registerReviewDetails, { openModal: openReviewDetails }] = useModal();

const visible = ref<boolean>(false);

const showModal = () => {
  visible.value = true;
};

const handleOk = () => {
  const record = unref(formState);
  newsRelease({ ...record, releaseType: 'release' }).then(res => {
    if (res.code === 200) {
      createMessage.success(`发布成功`);
    } else {
      createMessage.error(`发布失败, ${res.message}`);
    }
    reload();
  });
  visible.value = false;
};

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  newsGetOneNews({ autoId: record.autoId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, { isUpdate: true, record: { ...data, newsClicks: record?.newsClicks } });
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

function handleSuccess({ params, isUpdate }) {
  if (isUpdate) {
    newsUpdate({ ...params, singlePageNews: true }).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  } else {
    newsAdd({ ...params, singlePageNews: true }).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  }
}

//新增编辑预览
function handleView({ params }) {
  const { newsDetailsList, newsSource, newsClicks, keywords } = params as Recordable;

  const appDetail = find(newsDetailsList, v => v.platformType === '30');

  const zgDetail = find(newsDetailsList, v => v.platformType === '20');

  const pDetail = find(newsDetailsList, v => v.platformType === '10');

  openView(true, {
    record: {
      appContent: appDetail && appDetail.newsDetailsContent,
      appTitle: appDetail && appDetail.newsDetailsTitle,
      appUrl: appDetail && appDetail.externalLinkAddress,
      zgContent: zgDetail && zgDetail.newsDetailsContent,
      zgUrl: zgDetail && zgDetail.externalLinkAddress,
      pContent: pDetail && pDetail.newsDetailsContent,
      pUrl: pDetail && pDetail.externalLinkAddress,
      source: newsSource,
      reading: newsClicks,
      keywords: keywords,
    },
  });
}

//播报预览
function handleBroadcast(record) {
  openBroadcast(true, { record });
}
//播报管理
function handleBroadcastManage(record) {
  openBroadcastManage(true, { record });
}

//新闻统计明细页面
function handleStatisticalDetails(record) {
  openStatisticalDetails(true, { record });
  ifcollect.value = true;
}

function handleRecordView(record) {
  openView(true, {
    record: {
      newsId: record?.newsId,
    },
  });
}

//发布
function handlePublish(record, releaseType) {
  const name = releaseType === 'release' ? '发布' : '撤销';
  if (releaseType === 'release') {
    formState.value = { ...record };
    showModal();
  } else {
    createConfirm({
      title: '信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: `确定${name}?`,
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          return await new Promise<void>(resolve => {
            newsRelease({ autoId: record.autoId, releaseType }).then(res => {
              if (res.code === 200) {
                createMessage.success(`${name}成功`);
              } else {
                createMessage.error(`${name}失败`);
              }
              reload();
              resolve();
            });
          });
        } catch {
          return console.log('Oops errors!');
        }
      },
    });
  }
}

//删除
function handleDelete(record) {
  createConfirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          newsDelete({ ...record, logicallyDelete: 'y' }).then(res => {
            if (res.code === 200) {
              createMessage.success('删除成功');
            } else {
              createMessage.error('删除失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//设置置顶
function handleTop(record) {
  openTop(true, { record });
}
//设置排序
function handleSort(record) {
  openSort(true, { record });
}

//设置置顶回调
function handleTopSuccess({ values, autoId }) {
  newsTop({ ...values, autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createMessage.success('操作成功');
      closeTop();
      reload();
    } else {
      createMessage.error(msg);
    }
  });
}
//设置排序回调
function handleSortSuccess({ values, autoId }) {
  const { newsTitle, referToAutoId, newsSequentialOptions } = values;
  const newsSequentialOptionsName = 'before' === newsSequentialOptions ? '之前' : '之后';
  //根据新闻业务id查询新闻信息
  getNewsMainTable({ autoId: referToAutoId }).then(res => {
    const { code, data, message: mes } = res;
    if (code === 200) {
      createConfirm({
        title: '操作提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: `确定将[${newsTitle}]设置在[${data?.newsTitle}]${newsSequentialOptionsName}嘛?确认后系统将自动修改排序号!`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            return await new Promise<void>(resolve => {
              setNewsSort({ autoId, newsSequentialOptions, referToAutoId }).then(res => {
                resolve();
                if (res.code === 200) {
                  createMessage.success('设置成功!');
                  reload();
                  closeSort();
                } else {
                  createMessage.error(`设置失败!${res.message}`);
                }
              });
            });
          } catch {
            return console.log('Oops errors!');
          }
        },
      });
    } else {
      createMessage.error(`设置失败!${mes}`);
    }
  });
}

function changeNextLevelFlag(val) {
  const nextLevelFlag = val?.target?.checked;
  getUnionTree({ nextLevelFlag }).then(res => {
    categoryOptions.value = res;
  });
  newsReleaseUnionlList({ nextLevelFlag }).then(res => {
    const { data } = res;
    queryCompanyOptions.value = data;
  });
}

function handleCancel() {
  infos.setVisible(false);
  infos.setRecord(null);
}

//返回上一页
function handleGoBack() {
  push(PageEnum.BASE_HOME_COPY);
}

//重新加载页面
async function handleReload() {
  await reloadPageData(async () => {
    const result = await getCategoryInfoByCategoryId({
      categoryId: unref(currentRoute)?.meta?.categoryIdListStr,
    });

    if (result) {
      categoryInfos.value = result;

      // 栏目存在，继续初始化其他数据
      changeNextLevelFlag({ target: { checked: false } });
    }

    return result;
  });
}

//重试处理
function handleRetry() {
  handleReload();
}

//新闻评论明细页面
function handleReviewDetails(record) {
  openReviewDetails(true, { record });
}

watch(
  () => infos.getVisible,
  () => {
    auditVisible.value = infos.getVisible;
  }
);

watch(line, async () => {
  if (unref(line)?.newsId) {
    auditLines.value = await NewsAuditRecord({
      newsId: unref(line)?.newsId,
    });
  }
});

// 自动请求并暴露内部方法
onMounted(async () => {
  await initPageData(async () => {
    // 获取栏目信息
    const categoryResult = await getCategoryInfoByCategoryId({
      categoryId: unref(currentRoute)?.meta?.categoryIdListStr,
    });

    if (categoryResult) {
      categoryInfos.value = categoryResult;
      // 栏目存在，继续初始化其他数据
      changeNextLevelFlag({ target: { checked: false } });
    }

    return categoryResult;
  });
});
</script>
