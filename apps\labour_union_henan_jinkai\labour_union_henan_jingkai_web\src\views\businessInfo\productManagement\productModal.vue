<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :wrap-class-name="$style['product-info-modal']"
  >
    <a-tabs
      v-model:activeKey="activeTab"
      type="card"
    >
      <a-tab-pane
        key="basic"
        tab="基本信息"
      >
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
        />
      </a-tab-pane>

      <a-tab-pane
        key="specs"
        tab="规格信息"
      >
        <ProductSpecsList
          v-model:value="productSpecs"
          :disabled="disabled"
        />
      </a-tab-pane>
    </a-tabs>

    <CompaniesModal
      @register="registerCompanyModal"
      :canFullscreen="false"
      width="50%"
      @success="handleSuccess"
    />

    <CompaniesNextModal
      @register="registerCompanyNextModal"
      :canFullscreen="false"
      width="50%"
      @success="handleNextSuccess"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal, useModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import CompaniesModal from './CompaniesModal.vue';
import CompaniesNextModal from './CompaniesNextModal.vue';
import ProductSpecsList from './ProductSpecsList.vue';
import { join, split } from 'lodash-es';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const productSpecs = ref<any[]>([]);

const activeTab = ref<string>('basic');

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.productName || ''}--详情`
      : `编辑${unref(record)?.productName || ''}`
    : '新增积分商品';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(choiceCompany);
});

// 注册商户表单
const [registerCompanyModal, { openModal: openCompanyModal, closeModal: closeCompanyModal }] =
  useModal();

// 注册二级商户表单
const [
  registerCompanyNextModal,
  { openModal: openCompanyNextModal, closeModal: closeCompanyNextModal },
] = useModal();

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  // 重置到基本信息标签页
  activeTab.value = 'basic';

  // 重置规格数据
  productSpecs.value = [];

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      productPublicityImg: data.record.productPublicityImg
        ? split(data.record.productPublicityImg, ',')
        : '',
    });

    // 如果有规格数据，设置规格列表
    if (data.record.productSpecs && Array.isArray(data.record.productSpecs)) {
      productSpecs.value = [...data.record.productSpecs];
    }
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

// 选择商户
function choiceCompany(typeFlag: boolean) {
  typeFlag ? openCompanyModal(true, {}) : openCompanyNextModal(true, {});
}

// 确认商户
async function handleSuccess({ record }) {
  await setFieldsValue({
    companyName: record.companyName,
    companyId: record.companyId,
  });
  closeCompanyModal();
}

// 确认二级商户
async function handleNextSuccess({ companyNames, companyIds }) {
  await setFieldsValue({
    childProductCompanyName: companyNames,
    childProductCompany: companyIds,
  });
  closeCompanyNextModal();
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        productPublicityImg: values.productPublicityImg
          ? join(values.productPublicityImg, ',')
          : '',
        productSpecs: unref(productSpecs),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.product-info-modal {
  :global {
    .ant-modal-body {
      max-height: 70vh;
      overflow: auto;
      padding: 16px;
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }

    .ant-tabs-tabpane {
      outline: none;
    }
  }
}
</style>
