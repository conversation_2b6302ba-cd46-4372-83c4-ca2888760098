<template>
  <div class="product-specs-list">
    <div class="specs-header">
      <a-button
        type="primary"
        size="small"
        @click="addSpec"
        :disabled="disabled"
        class="ml-1 mt-1"
      >
        添加规格
      </a-button>
    </div>

    <div
      class="specs-table"
      v-if="specsList.length > 0"
    >
      <div class="table-header">
        <div class="col-index">序号</div>
        <div class="col-name">规格名</div>
        <div class="col-cover">规格封面</div>
        <div class="col-stock-type">库存类型</div>
        <div class="col-stock">库存量(件)</div>
        <div class="col-points">消耗积分</div>
        <div class="col-price">商品价格(元)</div>
        <div class="col-action w-[50px]">操作</div>
      </div>

      <div class="table-body">
        <div
          v-for="(spec, index) in specsList"
          :key="spec.id || index"
          class="table-row"
        >
          <div class="col-index">{{ index + 1 }}</div>
          <div class="col-name">
            <a-input
              v-model:value="spec.specName"
              placeholder="请输入规格名"
              :disabled="disabled"
              size="small"
            />
          </div>
          <div class="col-cover">
            <a-upload
              v-model:file-list="spec.coverFileList"
              :show-upload-list="false"
              :before-upload="() => false"
              :disabled="disabled"
              @change="info => handleCoverChange(info, index)"
            >
              <div class="upload-area">
                <img
                  v-if="spec.specCover"
                  :src="spec.specCover"
                  alt="规格封面"
                  class="cover-img"
                />
                <div
                  v-else
                  class="upload-placeholder"
                >
                  <PlusOutlined />
                  <div>上传</div>
                </div>
              </div>
            </a-upload>
          </div>
          <div class="col-stock-type">
            <a-select
              v-model:value="spec.stockType"
              placeholder="选择库存类型"
              :disabled="disabled"
              size="small"
              style="width: 100%"
            >
              <a-select-option value="limited">有限库存</a-select-option>
              <a-select-option value="unlimited">无限库存</a-select-option>
            </a-select>
          </div>
          <div class="col-stock">
            <a-input-number
              v-model:value="spec.stockCount"
              placeholder="库存量"
              :disabled="disabled || spec.stockType === 'unlimited'"
              :min="0"
              size="small"
              style="width: 100%"
            />
            <span
              v-if="spec.stockType === 'unlimited'"
              class="unlimited-text"
              >无限</span
            >
          </div>
          <div class="col-points">
            <a-input-number
              v-model:value="spec.consumePoints"
              placeholder="消耗积分"
              :disabled="disabled"
              :min="0"
              size="small"
              style="width: 100%"
            />
          </div>
          <div class="col-price">
            <a-input-number
              v-model:value="spec.price"
              placeholder="商品价格"
              :disabled="disabled"
              :min="0"
              :precision="2"
              size="small"
              style="width: 100%"
            />
          </div>
          <div class="col-action w-[50px]">
            <a-button
              type="text"
              danger
              size="small"
              @click="removeSpec(index)"
              :disabled="disabled"
              title="删除"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <div
      v-else
      class="empty-state"
    >
      <div class="empty-icon">📦</div>
      <div class="empty-text">暂无规格</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';

interface ProductSpec {
  id?: string;
  specName: string;
  specCover: string;
  coverFileList: any[];
  stockType: 'limited' | 'unlimited';
  stockCount: number;
  consumePoints: number;
  price: number;
}

interface Props {
  value?: ProductSpec[];
  disabled?: boolean;
}

interface Emits {
  (e: 'update:value', value: ProductSpec[]): void;
  (e: 'change', value: ProductSpec[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  disabled: false,
});

const emit = defineEmits<Emits>();

const specsList = ref<ProductSpec[]>([]);

// 监听外部值变化
watch(
  () => props.value,
  newValue => {
    if (newValue && Array.isArray(newValue)) {
      specsList.value = [...newValue];
    }
  },
  { immediate: true, deep: true }
);

// 监听内部值变化
watch(
  specsList,
  newValue => {
    emit('update:value', newValue);
    emit('change', newValue);
  },
  { deep: true }
);

// 添加规格
function addSpec() {
  const newSpec: ProductSpec = {
    id: Date.now().toString(),
    specName: '',
    specCover: '',
    coverFileList: [],
    stockType: 'limited',
    stockCount: 0,
    consumePoints: 0,
    price: 0,
  };
  specsList.value.push(newSpec);
}

// 删除规格
function removeSpec(index: number) {
  specsList.value.splice(index, 1);
}

// 处理封面上传
function handleCoverChange(info: UploadChangeParam, index: number) {
  const file = info.file;
  if (file && file.originFileObj) {
    const reader = new FileReader();
    reader.onload = e => {
      if (e.target?.result) {
        specsList.value[index].specCover = e.target.result as string;
        specsList.value[index].coverFileList = [file];
      }
    };
    reader.readAsDataURL(file.originFileObj);
  }
}
</script>

<style lang="less" scoped>
.product-specs-list {
  .specs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .specs-title {
      font-weight: 500;
      font-size: 14px;
    }
  }

  .specs-table {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;

    .table-header {
      display: grid;
      grid-template-columns: 60px 120px 100px 100px 100px 100px 120px 50px;
      background-color: #fafafa;
      border-bottom: 1px solid #d9d9d9;

      > div {
        padding: 12px 8px;
        font-weight: 500;
        font-size: 14px;
        text-align: center;
        border-right: 1px solid #d9d9d9;

        &:last-child {
          border-right: none;
        }
      }
    }

    .table-body {
      .table-row {
        display: grid;
        grid-template-columns: 60px 120px 100px 100px 100px 100px 120px 50px;
        border-bottom: 1px solid #d9d9d9;

        &:last-child {
          border-bottom: none;
        }

        > div {
          padding: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #d9d9d9;
          min-height: 60px;

          &:last-child {
            border-right: none;
          }
        }

        .col-index {
          font-weight: 500;
        }

        .col-name,
        .col-stock-type,
        .col-stock,
        .col-points,
        .col-price {
          padding: 4px;
          flex-direction: column;

          .unlimited-text {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
          }
        }

        .col-cover {
          .upload-area {
            width: 50px;
            height: 50px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.3s;

            &:hover {
              border-color: #1890ff;
            }

            .cover-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 4px;
            }

            .upload-placeholder {
              display: flex;
              flex-direction: column;
              align-items: center;
              color: #999;
              font-size: 12px;

              .anticon {
                font-size: 16px;
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: 14px;
    }
  }
}
</style>
