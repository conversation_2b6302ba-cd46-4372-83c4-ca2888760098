<template>
  <div class="product-specs-list">
    <div class="specs-toolbar">
      <a-button
        type="primary"
        size="small"
        @click="addSpec"
        :disabled="disabled"
      >
        添加规格
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="specsList"
      :pagination="false"
      size="small"
      :scroll="{ x: 800 }"
      :locale="{ emptyText: '暂无规格数据' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'index'">
          {{ index + 1 }}
        </template>

        <template v-if="column.key === 'productSubName'">
          <a-input
            v-model:value="record.productSubName"
            placeholder="请输入规格名"
            :disabled="disabled"
            size="small"
          />
        </template>

        <template v-if="column.key === 'productSubImg'">
          <CropperForm
            :operateType="65"
            :disabled="disabled"
            :value="record.productSubImg"
            @change="e => (record.productSubImg = e)"
          />
        </template>

        <template v-if="column.key === 'saleNum'">
          <a-select
            v-model:value="record.saleNum"
            placeholder="选择库存类型"
            :disabled="disabled"
            size="small"
            style="width: 100%"
          >
            <a-select-option value="limited">有限库存</a-select-option>
            <a-select-option value="unlimited">无限库存</a-select-option>
          </a-select>
        </template>

        <template v-if="column.key === 'reserve'">
          <div class="stock-input">
            <a-input-number
              v-model:value="record.reserve"
              placeholder="库存量"
              :disabled="disabled || record.saleNum === 'unlimited'"
              :min="0"
              size="small"
              style="width: 100%"
              v-if="record.saleNum !== 'unlimited'"
            />
            <span
              v-else
              class="unlimited-text"
              >无限</span
            >
          </div>
        </template>

        <template v-if="column.key === 'nowIntegral'">
          <a-input-number
            v-model:value="record.nowIntegral"
            placeholder="消耗积分"
            :disabled="disabled"
            :min="0"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-if="column.key === 'nowPrice'">
          <a-input-number
            v-model:value="record.nowPrice"
            placeholder="商品价格"
            :disabled="disabled"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-if="column.key === 'action'">
          <a-button
            type="text"
            danger
            size="small"
            @click="removeSpec(index)"
            :disabled="disabled"
            title="删除"
          >
            <DeleteOutlined />
          </a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, h } from 'vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { CropperForm } from '@/components/Cropper';

interface ProductSpec {
  id?: string;
  productSubName: string;
  productSubImg: string;
  coverFileList: any[];
  saleNum: 'limited' | 'unlimited';
  reserve: number;
  nowIntegral: number;
  nowPrice: number;
}

interface Props {
  value?: ProductSpec[];
  disabled?: boolean;
}

interface Emits {
  (e: 'update:value', value: ProductSpec[]): void;
  (e: 'change', value: ProductSpec[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  disabled: false,
});

const emit = defineEmits<Emits>();

const specsList = ref<ProductSpec[]>([]);

// 表格列定义
const columns = computed<TableColumnType[]>(() => [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'center',
  },
  {
    title: h('span', ['规格名', h('span', { style: 'color: red' }, '*')]),
    key: 'productSubName',
    width: 120,
    align: 'center',
  },
  {
    title: h('span', ['规格封面', h('span', { style: 'color: red' }, '*')]),
    key: 'productSubImg',
    width: 100,
    align: 'center',
  },
  {
    title: h('span', ['库存类型', h('span', { style: 'color: red' }, '*')]),
    key: 'saleNum',
    width: 100,
  },
  {
    title: h('span', ['库存量(件)', h('span', { style: 'color: red' }, '*')]),
    key: 'reserve',
    width: 100,
  },
  {
    title: h('span', ['消耗积分', h('span', { style: 'color: red' }, '*')]),
    key: 'nowIntegral',
    width: 100,
  },
  {
    title: h('span', ['商品价格(元)', h('span', { style: 'color: red' }, '*')]),
    key: 'nowPrice',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 50,
    align: 'center',
    fixed: 'right',
  },
]);

// 监听外部值变化
watch(
  () => props.value,
  newValue => {
    if (newValue && Array.isArray(newValue)) {
      specsList.value = [...newValue];
    }
  },
  { immediate: true, deep: true }
);

// 监听内部值变化
watch(
  specsList,
  newValue => {
    emit('update:value', newValue);
    emit('change', newValue);
  },
  { deep: true }
);

// 添加规格
function addSpec() {
  const newSpec: ProductSpec = {
    id: Date.now().toString(),
    productSubName: '',
    productSubImg: '',
    coverFileList: [],
    saleNum: 'limited',
    reserve: 0,
    nowIntegral: 0,
    nowPrice: 0,
  };
  specsList.value.push(newSpec);
}

// 删除规格
function removeSpec(index: number) {
  specsList.value.splice(index, 1);
}

// 处理封面上传
function handleCoverChange(info: UploadChangeParam, index: number) {
  const file = info.file;
  if (file && file.originFileObj) {
    const reader = new FileReader();
    reader.onload = e => {
      if (e.target?.result) {
        specsList.value[index].productSubImg = e.target.result as string;
        specsList.value[index].coverFileList = [file];
      }
    };
    reader.readAsDataURL(file.originFileObj);
  }
}
</script>

<style lang="less" scoped>
.product-specs-list {
  .specs-toolbar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }

  .upload-area {
    width: 50px;
    height: 50px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }

    .cover-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #999;
      font-size: 12px;

      .anticon {
        font-size: 16px;
        margin-bottom: 4px;
      }
    }
  }

  .stock-input {
    display: flex;
    flex-direction: column;
    align-items: center;

    .unlimited-text {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
}
</style>
