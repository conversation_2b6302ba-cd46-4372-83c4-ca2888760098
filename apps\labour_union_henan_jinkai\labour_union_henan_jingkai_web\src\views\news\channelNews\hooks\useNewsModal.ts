import { useRouter } from 'vue-router';
import { ref, unref, computed, inject } from 'vue';
import dayjs from 'dayjs';

/**
 * 新闻模态框状态管理 Hook
 */
export function useNewsModal() {
  const { currentRoute } = useRouter();

  // 基础状态
  const isUpdate = ref(false);
  const autoId = ref();
  const record = ref<Recordable>();
  const addType = ref();
  const ifFilter = ref(true);

  // 栏目相关状态
  const categoryId = ref<string[]>([]);
  const category = ref({});
  const categoryType = ref<string>('universal');
  const categoryPlatformType = ref<string>();
  const parentAndChildName = ref('');

  // 封面图相关状态
  const appCoverWhether = ref('');
  const appMaxCoverNumber = ref();
  const gwCoverWhether = ref('');
  const gwMaxCoverNumber = ref();

  // 排序相关
  const categorySortOrder = ref();

  // 裁剪相关
  const imageSize = ref(1);
  const imageSizeName = ref('');

  // URL 相关
  const isUrl = ref<boolean>(false);

  // Tab 相关
  const tabsPane = ref<Recordable[]>([]);
  const detailList = ref<Recordable[]>([]);
  const activeKey = ref<string[]>(['activeKey-1', 'activeKey-2']);
  const columnList = ref<string[]>([]);
  const activeTabs = ref<string>('');

  // 树形组件相关
  const expand = ref<string[]>([]);

  const categoryInfo = inject<Recordable>('categoryInfos', {});

  // 计算属性
  const isUploadFiles = computed(() => {
    return unref(categoryType) === 'UPLOAD_FILES';
  });

  const title = computed(() => {
    return unref(isUpdate) ? `编辑--${unref(record)?.newsTitle || ''}` : '新增新闻';
  });

  const ifCategoryId = computed(() => {
    return !!unref(currentRoute)?.meta?.categoryId;
  });

  const routeCategoryId = computed(() => {
    return !!unref(currentRoute)?.meta?.categoryId;
  });

  /**
   * 重置所有状态
   */
  const resetStates = () => {
    detailList.value = [];
    tabsPane.value = [];
    activeTabs.value = '';
    activeKey.value = ['activeKey-1', 'activeKey-2'];
    categoryId.value = [];
    isUrl.value = false;
    isUpdate.value = false;
    autoId.value = undefined;
    imageSizeName.value = '';
    imageSize.value = 1;
    parentAndChildName.value = '';
    categoryType.value = 'universal';
    ifFilter.value = true;
  };

  /**
   * 初始化编辑数据
   */
  const initEditData = (data: Recordable) => {
    const {
      autoId: id,
      parentAndChildName: pname,
      newsDetailsList,
      categoryInfo,
      whetherExternalLink,
      categoryId: cId,
      publishTime,
      aiVoiceAddress,
      aiVideoAddress,
    } = data;

    autoId.value = id;
    isUrl.value = !!whetherExternalLink;
    parentAndChildName.value = pname;
    detailList.value = newsDetailsList;
    categoryId.value = [cId];
    expand.value = [cId];
    columnList.value = [cId];
    categoryType.value = categoryInfo.categoryType;
    categoryPlatformType.value = categoryInfo.platformType;

    return {
      ...data,
      publishTime: publishTime ? dayjs(publishTime) : dayjs(),
      aiVoiceAddress: aiVoiceAddress ? aiVoiceAddress.split(',') : [],
      aiVideoAddress: aiVideoAddress ? aiVideoAddress.split(',') : [],
    };
  };

  /**
   * 初始化新增数据
   */
  const initAddData = () => {
    categorySortOrder.value = undefined;
    categoryPlatformType.value = undefined;
    return { publishTime: dayjs() };
  };

  return {
    // 状态
    isUpdate,
    autoId,
    record,
    addType,
    ifFilter,
    categoryId,
    category,
    categoryType,
    categoryPlatformType,
    parentAndChildName,
    appCoverWhether,
    appMaxCoverNumber,
    gwCoverWhether,
    gwMaxCoverNumber,
    categorySortOrder,
    imageSize,
    imageSizeName,
    isUrl,
    tabsPane,
    detailList,
    activeKey,
    columnList,
    activeTabs,
    expand,
    categoryInfo,
    // 计算属性
    isUploadFiles,
    title,
    ifCategoryId,
    routeCategoryId,
    // 方法
    resetStates,
    initEditData,
    initAddData,
  };
}
