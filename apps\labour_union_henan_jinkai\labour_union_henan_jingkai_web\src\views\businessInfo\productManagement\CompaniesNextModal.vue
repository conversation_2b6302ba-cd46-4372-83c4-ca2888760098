<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :canFullscreen="false"
    @cancel="handleCancel"
    @ok="handleChoiceCompany"
  >
    <BasicTable
      @register="registerTable"
      :click-to-row-select="true"
    />
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable } from '@/components/Table';
import { computed, h } from 'vue';
import { Image } from 'ant-design-vue';
import { join, map, startsWith } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { list } from '@/api/merchants';

const emit = defineEmits(['success', 'register']);

const userStore = useUserStore();
const dictionary = useDictionary();

const title = computed(() => {
  return `核销商户选择`;
});

const [registerModal, {}] = useModalInner(async date => {
  clearSelectedRowKeys();
  await reload();
});

const [registerTable, { reload, getForm, clearSelectedRowKeys, getSelectRows }] = useTable({
  rowKey: 'merchantsInfoId',
  api: list,
  columns: [
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 100,
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return h(Image, {
          src: startsWith(text, 'http') ? text : userStore.getPrefix + text,
          width: 50,
          height: 50,
        });
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
      width: 100,
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 100,
      customRender: ({ text }) => {
        return h('span', dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName);
      },
    },
    {
      title: '商户状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text, record }) => {
        const { state } = record;
        return h(
          'span',
          {
            class: state === 'NORMAL' ? 'text-green-500' : 'text-red-500',
          },
          dictionary.getDictionaryMap.get(`company_state_${text}`)?.dictName
        );
      },
    },
  ],

  formConfig: {
    labelWidth: 90,
    autoSubmitOnEnter: true,
    schemas: [
      {
        field: 'companyName',
        label: '商户名称',
        component: 'Input',
        colProps: { span: 8 },
        componentProps: {
          placeholder: '请输入商户名称',
          autocomplete: 'off',
        },
      },
    ],
  },
  rowSelection: {
    type: 'checkbox',
  },
  maxHeight: 400,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

// 选择按钮操作
function handleChoiceCompany() {
  emit('success', {
    companyNames: join(map(getSelectRows(), v => v.companyName)),
    companyIds: map(getSelectRows(), v => v.companyId),
  });
}

// 关闭时执行
function handleCancel() {
  getForm()?.resetFields();
}
</script>
