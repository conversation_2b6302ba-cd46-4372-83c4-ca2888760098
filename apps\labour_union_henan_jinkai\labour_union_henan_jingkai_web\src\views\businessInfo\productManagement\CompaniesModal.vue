<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
    @cancel="handleCancel"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleChoiceCompany.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed, h } from 'vue';
import { Image } from 'ant-design-vue';
import { startsWith } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { list } from '@/api/merchants';

const emit = defineEmits(['success', 'register']);

const userStore = useUserStore();
const dictionary = useDictionary();

const title = computed(() => {
  return `核销商户选择`;
});

const [registerModal, {}] = useModalInner(async date => {
  await reload();
});

const [registerTable, { reload, getForm }] = useTable({
  rowKey: 'merchantsInfoId',
  api: list,
  columns: [
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 100,
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return h(Image, {
          src: startsWith(text, 'http') ? text : userStore.getPrefix + text,
          width: 50,
          height: 50,
        });
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
      width: 100,
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 100,
      customRender: ({ text }) => {
        return h('span', dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName);
      },
    },
    {
      title: '商户状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text, record }) => {
        const { state } = record;
        return h(
          'span',
          {
            class: state === 'NORMAL' ? 'text-green-500' : 'text-red-500',
          },
          dictionary.getDictionaryMap.get(`company_state_${text}`)?.dictName
        );
      },
    },
  ],

  formConfig: {
    labelWidth: 90,
    autoSubmitOnEnter: true,
    schemas: [
      {
        field: 'companyName',
        label: '商户名称',
        component: 'Input',
        colProps: { span: 8 },
        componentProps: {
          placeholder: '请输入商户名称',
          autocomplete: 'off',
        },
      },
    ],
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
  maxHeight: 400,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleChoiceCompany(record) {
  emit('success', { record: record });
}

//关闭时执行
function handleCancel() {
  getForm()?.resetFields();
}
</script>
