import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Button, CheckboxOptionType, Image, Input } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { Tinymce } from '@/components/Tinymce';
import { uploadApi } from '@/api/sys/upload';

const dictionary = useDictionary();

const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
    },
    {
      title: '一级商户',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 90,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '商品栏目',
      dataIndex: 'integralProductColumn',
      width: 90,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`integralProductColumn_${text}`)?.dictName}</span>
        );
      },
    },
    {
      title: '上架状态',
      dataIndex: 'state',
      width: 90,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span class={state === 'up' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}>
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'integralProductColumn',
      label: '商品栏目',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('integralProductColumn'),
        };
      },
    },
    {
      field: 'state',
      label: '上架状态',
      component: 'RadioGroup',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            {
              label: '全部',
              value: undefined,
            },
            ...(dictionary.getDictionaryOpt.get('saleEnable') as RadioGroupChildOption[]),
          ],
        };
      },
    },
  ];
};

export const modalFormItem = (choiceCompany: Fn): FormSchema[] => {
  return [
    {
      field: 'productName',
      label: '商品名称',
      colProps: { span: 24 },
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'productType',
      label: '商品类型',
      colProps: { span: 12 },
      required: true,
      component: 'RadioGroup',
      defaultValue: 'actual',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('productType') as CheckboxOptionType[],
        };
      },
    },
    {
      field: 'integralPayment',
      label: '领取方式',
      colProps: { span: 12 },
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: '1',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('integralPayment') as RadioGroupChildOption[],
      },
    },

    //自主提供选择商家
    {
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      render({ model, field }) {
        return (
          <Input value={model[field]}>
            {{
              suffix: () => (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => choiceCompany(true)}
                >
                  选择一级商户
                </Button>
              ),
            }}
          </Input>
        );
      },
    },
    // {
    //   field: 'contractPhone',
    //   label: '商家电话',
    //   required: true,
    //   colProps: { span: 12 },
    //   component: 'Input',
    //   dynamicDisabled: true,
    // },
    {
      field: 'companyId',
      label: '商户id',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      show: false,
    },

    {
      field: 'childProductCompanyName',
      label: '二级商户',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      ifShow({ values }) {
        return !!values.companyId;
      },
      render({ model, field }) {
        return (
          <Input value={model[field]}>
            {{
              suffix: () => (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => choiceCompany(false)}
                >
                  选择二级商户
                </Button>
              ),
            }}
          </Input>
        );
      },
    },
    {
      field: 'childProductCompany',
      label: '二级商户ID',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      show: false,
    },
    {
      field: 'consumeType',
      label: '消耗类型',
      colProps: { span: 12 },
      required: true,
      component: 'RadioGroup',
      defaultValue: 'integral',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('consumeType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'maxReceiveNum',
      label: '最多领取次数',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
      },
    },

    {
      field: 'integralProductColumn',
      label: '商品栏目',
      colProps: { span: 12 },
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: '1',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('integralProductColumn') as CheckboxOptionType[],
      },
    },
    {
      field: 'productCoverImg',
      label: '产品封面图',
      colProps: { span: 12 },
      component: 'CropperForm',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        operateType: 65,
      },
    },
    {
      field: 'productPublicityImg',
      label: '产品宣传图',
      colProps: { span: 12 },
      component: 'Upload',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 5,
        uploadParams: {
          operateType: 65,
        },
      },
    },
    // {
    //   field: 'address',
    //   required: true,
    //   label: '商户地址',
    //   component: 'MapSelect',
    //   rest: true,
    //   colProps: { span: 24 },
    //   rulesMessageJoinLabel: true,
    //   componentProps({ formModel }) {
    //     return {
    //       onChangeLnglat: lnglat => (formModel['addressCoordinate'] = lnglat),
    //       lnglat: formModel['addressCoordinate'],
    //       onChange: address => (formModel['address'] = address),
    //       value: formModel['address'],
    //     };
    //   },
    // },
    // {
    //   field: 'addressCoordinate',
    //   label: '商户坐标',
    //   colProps: { span: 24 },
    //   component: 'ShowSpan',
    //   rulesMessageJoinLabel: true,
    //   show: false,
    // },
    {
      field: 'exchangeNotice',
      label: '兑换须知',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      defaultValue:
        '兑后48小时内发货，确认兑换过后积分不退港澳台、海外、受其他外在影响或突发情况地区暂不支持发货。',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 500,
        rows: 4,
      },
    },
    {
      field: 'productIntroduce',
      label: '商品简介',
      colProps: { span: 24 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      rest: true,
      render({ model, field, disabled }) {
        return (
          <Tinymce
            value={model[field]}
            options={{ readonly: !!disabled }}
            onChange={value => {
              model[field] = value;
            }}
          />
        );
      },
    },
  ];
};
