import {BasicColumn, FormSchema} from '@/components/Table';
import {h} from "vue";
import {Tinymce} from "@/components/Tinymce";
import {cloneDeep, isObject, isString, map} from "lodash-es";
import {RadioGroupChildOption} from "ant-design-vue/es/radio/Group";
import {useDictionary} from "@/store/modules/dictionary";
import {Image, Tooltip} from "ant-design-vue";
import {uploadApi} from "@/api/sys/upload";
import {useUserStore} from "@/store/modules/user";


export const columns = (): BasicColumn[] => {
  return [
    {
      title: '作品编号',
      dataIndex: 'opusNo',
      width: 120,
    },
    {
      title: '作品名称',
      dataIndex: 'opusName',
    },
    {
      title: '作者姓名',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '工作单位',
      dataIndex: 'workUnit',
      width: 200,
    },
    {
      title: '总票数',
      dataIndex: 'votesNum',
      width: 100,
    },
    {
      title: '今日新增票券',
      dataIndex: 'todayCount',
      width: 100,
    },
    {
      title: '排名',
      dataIndex: 'rank',
      width: 100,
    },
  ]
}

export const formSchemas = (voteTypes): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'opusName',
      label: '作品名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'opusNo',
      label: '作品编号',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'opusType',
      component: 'Select',
      label: '作品分类',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        const option = map(cloneDeep(voteTypes), ({recordId,opusType}) => {
          return {label:opusType,value:recordId}
        });
        return {
          options: option,
        };
      },
    },
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'areaName',
      label: '区域选择',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'),t=>{
          return {value:t.label,label:t.label}
        }) as RadioGroupChildOption[],
        placeholder: '请选择参与区域',
      },
    },
  ];
};

export const modalForm = (isUpdate,isAudit,activityInfo): FormSchema[] => {
  const dictionary = useDictionary()
  const userStore = useUserStore()
  const {voteInfo:{voteTypeConfigList}} = activityInfo

  return [
    {
      field: 'opusName',
      label: '作品名称',
      component: 'Input',
      required: true,
    },
    {
      field: 'opusNo',
      label: '作品编号',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      ifShow:!isAudit,
    },
    {
      field: 'orderNum',
      label: '序号',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      ifShow:!isAudit,
      componentProps:{
        step:1,
        min:0,
      }
    },
    {
      field: 'userName',
      label: '作者姓名',
      component: 'Input',
      required: false,
      colProps: { span: 12 },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      required: false,
      colProps: { span: 12 },
    },
    {
      field: 'workUnit',
      label: '工作单位',
      component: 'Input',
      required: false,
      colProps: { span: 12 },
    },
    {
      field: 'areaName',
      label: '区域选择',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'),t=>{
          return {value:t.label,label:t.label}
        }) as RadioGroupChildOption[],
        placeholder: '请选择参与区域',
      },
    },
    {
      field: 'opusCover',
      label: '封面图',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      required: true,
      renderComponentContent() {
        return {
          tip: () => (
              <div class="text-sm leading-7">
                注:图标规格大小为(<span class="text-red-500">355 * 164</span>)
              </div>
          ),
        };
      },
      componentProps: function () {
        return {
          imgSize: 335 / 164,
          operateType: 17,
        };
      },
    },
    {
      field: 'opusType',
      component: 'Select',
      label: '作品分类',
      rulesMessageJoinLabel: true,
      required: true,
      colProps: {
        span: 12,
      },
      componentProps: ({formModel}) => {
        const options = map(cloneDeep(voteTypeConfigList), ({recordId,opusType}) => {
          return {label:opusType,value:recordId}
        });
        return {
          placeholder: '请选择作品分类',
          options,
          onChange: e => {
            const voteTypeConfig = voteTypeConfigList.find(t=>t.recordId === e)
            if(voteTypeConfig){
              formModel['fileType'] = voteTypeConfig.fileType;
              formModel['fileLimit'] = voteTypeConfig.fileLimit;
            }
          },
        };
      },
    },
    {
      field: 'fileType',
      label: '作品类型',
      component: 'Input',
      show:false,
    },
    {
      field: 'fileLimit',
      label: '作品数量',
      component: 'Input',
      show:false,
    },
    {
      field: 'opusImages',
      label: '上传图片',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      ifShow({values}){return !isAudit && values.fileType && values.fileType.includes('img')},
      componentProps({ formModel }) {
        return  {
          api: uploadApi,
          uploadParams: {
            operateType: 17,
          },
          maxNumber: Number(formModel['fileLimit']) ,
          maxSize: 10 ,
          accept:['image/*'],
        }
      },
    },
    {
      field: 'opusImages',
      label: '上传图片',
      component: 'ShowSpan',
      ifShow({values}){return !isUpdate && isAudit},
      render({ values }) {
        let img =
            values?.opusImages?.split(',').map(t => {
              return (
                <span style={{ marginRight: '10px' }}>
                  <Image
                      width={70}
                      height={70}
                      src={userStore.getPrefix + t}
                  />
                </span>
              );
            }) ?? '';
       return (<div>{img}</div>)
      },
    },
    {
      field: 'opusFiles',
      label: '上传视频',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      ifShow({values}){return values.fileType && values.fileType.includes('video')},
      componentProps({ formModel }) {
        return  {
          api: uploadApi,
          uploadParams: {
            operateType: 17,
          },
          maxNumber:1,
          maxSize: 500,
          accept: ['video/*'],
        }
      },
    },
    {
      field: 'opusContent',
      label: '作品简介',
      component: 'Input',
      colProps: { span: 24 },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          options: {
            readonly: disabled,
          },
          operateType: 17,
        });
      },
    },
  ]
}


export const auditColumns = (): BasicColumn[] => {
  return [
    {
      title: '作品名称',
      dataIndex: 'opusName',
    },
    {
      title: '作者姓名',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '工作单位',
      dataIndex: 'workUnit',
      width: 200,
    },
    {
      title: '所属区域',
      dataIndex: 'areaName',
      width: 120,
    },
    {
      title: '上传日期',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '审核状态',
      dataIndex: 'state',
      width: 120,
      customRender: ({ record }) => {
        const dictionary = useDictionary();
        const name = dictionary.getDictionaryMap.get(`auditState_${record.state}`)?.dictName;
        return <Tooltip title={name}>{name ?? '-'}</Tooltip>;
      },
    },
    {
      title: '审核意见',
      dataIndex: 'opinion',
      width: 150,
    },
  ]
}


export const auditFormSchemas = (voteTypes): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'state',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('auditState'),
        };
      },
    },
    {
      field: 'opusName',
      label: '作品名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'opusType',
      component: 'Select',
      label: '作品分类',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        const option = map(cloneDeep(voteTypes), ({recordId,opusType}) => {
          return {label:opusType,value:recordId}
        });
        return {
          options: option,
        };
      },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'areaName',
      label: '区域选择',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'),t=>{
          return {value:t.label,label:t.label}
        }) as RadioGroupChildOption[],
        placeholder: '请选择参与区域',
      },
    },
  ];
};


export const auditModalForm = (): FormSchema[] => {
  return [
    {
      field: 'opusName',
      label: '作品名称',
      component: 'Input',
      required: true,
    },

    {
      field: 'userName',
      label: '作者姓名',
      component: 'Input',
      required: false,
      colProps: { span: 12 },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      required: false,
      colProps: { span: 12 },
    },
    {
      field: 'companyName',
      label: '组织单位',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'areaName',
      label: '所属区域',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'opusCover',
      label: '封面图',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      required: true,
      renderComponentContent() {
        return {
          tip: () => (
              <div class="text-sm leading-7">
                注:图标规格大小为(<span class="text-red-500">200 * 224</span>)以内
              </div>
          ),
        };
      },
      componentProps: function () {
        return {
          imgSize: 200 / 224,
          operateType: 17,
        };
      },
    },
    {
      field: 'opusContent',
      label: '作品简介',
      component: 'Input',
      colProps: { span: 24 },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          options: {
            readonly: disabled,
          },
          operateType: 17,
        });
      },
    },
  ]
}
