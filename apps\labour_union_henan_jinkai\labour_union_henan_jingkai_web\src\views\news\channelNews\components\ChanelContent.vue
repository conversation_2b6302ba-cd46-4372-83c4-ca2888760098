<template>
  <div class="chanel-content">
    <!-- 基础表单 -->
    <BasicForm
      :title="item.newsDetailsTitle"
      :abstract="item.newsDetailsAbstract"
      :externalLinkAddress="item.externalLinkAddress"
      :abstractRequired="item.abstractWhether === 'Y'"
      :isUrl="isUrl"
      @update:title="value => (item.newsDetailsTitle = value)"
      @update:abstract="value => (item.newsDetailsAbstract = value)"
      @update:externalLinkAddress="value => (item.externalLinkAddress = value)"
      @fieldChange="handleFormChange"
    />

    <!-- 资源配置（仅非URL模式） -->
    <ResourceConfig
      v-if="!isUrl"
      :whetherLinkResources="item.whetherLinkResources"
      :resourceType="item.resourceType"
      :externalAddress="item.externalAddress"
      :externalCoverUrl="item.externalCoverUrl"
      :activityName="item.activityName"
      :detailsWhetherPrompt="item.detailsWhetherPrompt"
      :resourceTypeOptions="newsResourceType as any"
      @update:whetherLinkResources="value => (item.whetherLinkResources = value)"
      @update:resourceType="value => (item.resourceType = value)"
      @update:externalAddress="value => (item.externalAddress = value)"
      @update:externalCoverUrl="value => (item.externalCoverUrl = value)"
      @update:activityName="value => (item.activityName = value)"
      @update:internalBusinessId="value => (item.internalBusinessId = value)"
      @update:detailsWhetherPrompt="value => (item.detailsWhetherPrompt = value)"
    />

    <!-- 关键词管理 -->
    <KeywordsManager
      :platformType="item.platformType"
      :content="item.newsDetailsContent"
      :record="record"
      :isUpdate="isUpdate"
      @getTags="handleTagsChange"
    />

    <!-- 内容编辑器（仅非URL模式） -->
    <ContentEditor
      v-if="!isUrl"
      :content="item.newsDetailsContent"
      :fileList="item.fileList"
      :isUploadFiles="isUploadFiles"
      :uploadConfig="uploadConfig"
      @update:content="value => (item.newsDetailsContent = value)"
      @update:fileList="value => (item.fileList = value)"
      @fileChange="handleFileChange"
    />
  </div>
</template>

<script lang="ts" setup>
import BasicForm from './BasicForm.vue';
import ResourceConfig from './ResourceConfig.vue';
import KeywordsManager from './KeywordsManager.vue';
import ContentEditor from './ContentEditor.vue';
import { useContentForm } from './hooks/useContentForm';
import { useResourceManager } from './hooks/useResourceManager';
import { useNewsSubmission } from './hooks/useNewsSubmission';

interface Props {
  isUrl?: boolean;
  item?: any;
  isUpdate?: boolean;
  isUploadFiles?: boolean;
  record?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isUrl: false,
  item: () => ({}),
  isUpdate: false,
  isUploadFiles: false,
  record: () => ({}),
});

const emit = defineEmits<{
  getTags: [tags: string[]];
}>();

// 使用表单管理 Hook
const { uploadConfig, newsResourceType, formState, markAsModified, validateForm } =
  useContentForm();

// 使用资源管理 Hook
const { handleFileChange: handleFileUpload } = useResourceManager();

// 使用新闻提交管理 Hook
const { processCoverImages, processSingleItemCoverImages } = useNewsSubmission();

// 处理表单字段变化
const handleFormChange = (field: string, value: string) => {
  markAsModified();

  // 可以在这里添加实时验证逻辑
  if (field === 'title' && !value?.trim()) {
    // 标题不能为空的提示
  }
};

// 处理文件上传
const handleFileChange = (changeInfo: any) => {
  handleFileUpload(changeInfo, props.item);
};

// 处理关键词变化
const handleTagsChange = (tags: string[]) => {
  emit('getTags', tags);
};

// 处理数据提交前的预处理
const prepareForSubmission = (submissionOptions?: any) => {
  try {
    // 处理单个新闻项的封面图
    const processedItem = processSingleItemCoverImages(props.item);

    return processedItem;
  } catch (error) {
    console.error('数据预处理失败:', error);
    throw error;
  }
};

// 获取处理后的新闻数据
const getProcessedNewsData = () => {
  return prepareForSubmission();
};

// 暴露验证方法
const validate = () => {
  return validateForm(props.item);
};

// 暴露给父组件的方法
defineExpose({
  validate,
  formState,
  prepareForSubmission,
  getProcessedNewsData,
  processCoverImages,
});
</script>

<style scoped>
.chanel-content {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.chanel-content > * + * {
  margin-top: 16px;
}
</style>
