<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAudit(null)">
          批量审核
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.state !== 'WAIT',
                onClick: handleAudit.bind(null, record),
              },
                {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ActivityCommentAudit
      @register="registerModule"
      width="50%"
      @success="handleSuccess"
    />
    <ActivityCommentApply
      @register="registerApply"
      width="45%"
      @success="handleSuccessApply"
    />
  </div>
</template>

<script lang="ts" setup>
import { useAttrs, computed, unref, h } from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { findCommentsList, auditComments, updateOpenState,deleteComment } from '@/api/activities';
import { commentColum, commentSearchFormSchema } from '../activity';
import { isArray, map } from 'lodash-es';
import { useModal } from '@/components/Modal';
import ActivityCommentAudit from './ActivityCommentAudit.vue';
import ActivityCommentApply from './ActivityCommentApply.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const attrs = useAttrs();

const { createSuccessModal, createErrorModal, createWarningModal, createConfirm } = useMessage();

const type = computed(() => {
  return attrs.type;
});

const commentType = computed(() => {
  return attrs.commentType as string;
});

const activityId =  computed(() => {
  return attrs.activityId;
});

//权限
const titleAuth = computed(() => {
  if (isArray(attrs?.titleAuth)) return attrs?.titleAuth as string[];
  return (attrs?.titleAuth || '') as string;
});

const columnAuth = computed(() => {
  return attrs?.columnAuth as string[];
});

const recordAuth = computed(() => {
  return attrs?.recordAuth as Recordable;
});

const columns = computed(() => {
  return commentColum();
});

const [registerTable, { reload, clearSelectedRowKeys, getSelectRows }] = useTable({
  rowKey: 'autoId',
  columns: columns,
  formConfig: {
    labelWidth: 120,
    schemas: commentSearchFormSchema(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    params.commentType = unref(commentType);
    if(unref(activityId)){
      params.activityId = unref(activityId)
    }else {
      params.activityCategory =  unref(type)
    }
    return params;
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled:
       record.state !== 'WAIT',
    }),
  },
  useSearchForm: !unref(activityId),
  showTableSetting: false,
  bordered: true,
  api: findCommentsList,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    width: 200,
  },
});

const [registerModule, { openModal, closeModal }] = useModal();

const [registerApply, { openModal: openApply }] = useModal();

function handleAudit(record) {
  let arr: Recordable[] = [];
  if (record) {
    arr.push(record.commentsId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' });
      return false;
    }
    arr = map(rows, v => v.commentsId);
  }
  openModal(true, { commentsIds: arr, record });
}

function handleApply(record) {
  openApply(true, { record });
}

async function handleSuccessApply() {
  await reload();
}

function handlePublish(record) {
  let type = 'Y';
  let text = '公开';
  let content;
  let commentsIds: string[] = [];

  if (!!record) {
    type = record.openState === 'Y' ? 'N' : 'Y';
    text = record.openState === 'Y' ? '撤销' : '公开';
    commentsIds.push(record.commentsId);

    content = h(`div`, [
      `请确认要${text}该评价`,
      h('div', `评价人：${record.createUser}`),
      h('div', `评价内容：${record.content}`),
    ]);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' });
      return false;
    }
    commentsIds = map(rows, v => v.commentsId);
    content = h(`div`, [`请确认要公开所选评价`]);
  }

  createConfirm({
    iconType: 'warning',
    content: content,
    onOk: function () {
      updateOpenState({
        ...record,
        openState: type,
        commentsIds,
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
          clearSelectedRowKeys();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values }) {
  auditComments({...values, activityCategory:unref(type)}).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: '审核成功!' || message });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({ content: `审核失败!${message}` });
    }
  });
}

function handleDelete(record){
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除？`,
    onOk: function () {
      deleteComment({autoId:record.autoId}).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}
</script>
