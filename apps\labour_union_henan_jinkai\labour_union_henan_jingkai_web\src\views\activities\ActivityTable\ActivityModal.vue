<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :wrapClassName="`${$style['activity-modal']} full-modal-self `"
  >
    <Tabs
      :activeKey="activeKey"
      @change="changeTabs"
    >
      <TabPane
        key="activity"
        tab="活动信息"
      >
        <BasicInfo @register="registerInfo">
          <template #customerType="{ model, field }">
            <Select
              v-model:value="model[field]"
              :disabled="disabled"
              :options="dictionary.getDictionaryOpt.get('customerType')"
              placeholder="请选择领取用户类型"
              @change="e => onChangeCustomerType(model, field, e)"
            ></Select>
          </template>
          <template #extendList="{ model, field }">
            <template v-if="!!model[field]">
              <Tag  v-for="(item) in model[field]" :key="item.sourceId" :closable="!disabled" @close="deleteUnion(model, field,item)">
                {{ item.sourceName }}
              </Tag>
            </template>
            <a-button v-if="!disabled" type="primary" @click="handleChooseUnion('union')">选择工会</a-button>
          </template>
          <template #ifQuiz="{ model, field }">
            <RadioGroup
              v-model:value="model[field]"
              :disabled="disabled || bigActivityType === BigActivityType.SIGN_UP_AND_QUIZ"
              @change="e => ifQuizChange(e)"
              :options="defaultCurrent"
            />
          </template>
          <template #activityTypeConfig="{ model, field }">
            <RadioGroup
              v-model:value="model[field]"
              :disabled="disabled || isUpdate"
              @change="e => changeActivityType(e, setProps, resetDefaultField)"
              :options="activityTypeConfig"
            />
          </template>
        </BasicInfo>
      </TabPane>
      <TabPane
        :key="`${ActivityType.QUIZ}`"
        :tab="`${ActivitySettingZh[ActivityType.QUIZ]}配置`"
        v-if="computedTabVisibility.showQuizTab"
        forceRender
      >
        <BasicSetting
          @register="registerQuiz"
          :activityType="ActivityType.QUIZ"
        />
      </TabPane>
      <TabPane
        :key="`${ActivityType.SIGNUP}`"
        :tab="`${ActivitySettingZh[ActivityType.SIGNUP]}配置`"
        v-if="computedTabVisibility.showSignupTab"
        forceRender
      >
        <BasicSetting
          @register="registerSignUp"
          :activityType="computedActivityType"
        />
      </TabPane>
      <!-- 健步走配置 -->
      <TabPane
        :key="`${ActivityType.WALK}`"
        :tab="`${ActivitySettingZh[ActivityType.WALK]}配置`"
        v-if="computedTabVisibility.showWalkTab"
        forceRender
      >
        <BasicSetting
          :activityId="activityId"
          @register="registerWalk"
          :activityType="ActivityType.WALK"
        />
      </TabPane>
      <TabPane
        :key="`${ActivityType.LOTTERY}`"
        :tab="`${computedTabVisibility.lotteryTabTitle}配置`"
        v-if="computedTabVisibility.showLotteryTab"
        forceRender
      >
        <BasicSetting
          @register="registerLottery"
          :activityId="activityId"
          :activityType="computedTabVisibility.lotteryActivityType"
          :mainType="computedActivityType"
        />
      </TabPane>

      <TabPane
        :key="`${ActivityType.SURVEY}`"
        :tab="`${ActivitySettingZh[ActivityType.SURVEY]}配置`"
        v-if="computedTabVisibility.showSurveyTab"
        forceRender
      >
        <BasicSetting
          :activityId="activityId"
          @register="registerSurvey"
          :activityType="ActivityType.SURVEY"
        />
      </TabPane>
      <TabPane
        :key="`${ActivityType.VOTE}`"
        :tab="`${ActivitySettingZh[computedActivityType]}配置`"
        v-if="computedTabVisibility.showVoteTab"
        forceRender
      >
        <BasicSetting
          :activityId="activityId"
          @register="registerVote"
          :mainType="computedActivityType"
          :activityType="computedActivityType"
        />
      </TabPane>
      <TabPane
        :key="`${ActivityType.INCLUSIVE_YJWD}`"
        :tab="`${ActivitySettingZh[ActivityType.INCLUSIVE_YJWD]}配置`"
        v-if="
          computedTabVisibility.showInclusiveTab &&
          computedActivityType === ActivityType.INCLUSIVE_YJWD
        "
        forceRender
      >
        <BasicSetting
          :activityId="activityId"
          @register="registerInclusiveYJWD"
          :activityType="ActivityType.INCLUSIVE_YJWD"
        />
      </TabPane>
      <!-- 票券配置 -->
      <TabPane
        :key="`${ActivityType.COUPON}`"
        :tab="`${ActivitySettingZh[ActivityType.COUPON]}配置`"
        v-if="computedTabVisibility.showCouponTab"
        forceRender
      >
        <BasicSetting
          :activityId="activityId"
          @register="registerInclusiveTicket"
          :activityType="computedActivityType"
        />
      </TabPane>
      <template
        #rightExtra
        v-if="shouldShowPluginSelector && !disabled"
        class="w-20vh"
      >
        <Tag color="blue">插件库</Tag>
        <Select
          v-model:value="otherTabs"
          class="w-60"
          mode="multiple"
          :options="otherOptions"
          @change="handleOthers"
          :disabled="disabled"
        />
      </template>
    </Tabs>
    <SensitiveCheckModal
      @register="registerSensitive"
      @success="handleCheck"
      :can-fullscreen="false"
      width="30%"
    />
    <CompanySelectModal
        @register="registerCompanySelectModal"
        width="70%"
        @success="handleCompanySelectSuccess"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, createVNode, inject, nextTick, ref, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { Modal, RadioGroup, Select, Tabs, Tag } from 'ant-design-vue';
import { ActivitySettingZh, ActivityType, BigActivityType, props as p } from '../activities.d';
import { useInfo } from './BasicInfo/useInfo';
import BasicInfo from './BasicInfo/index.vue';
import BasicSetting from './BasicSetting/index.vue';
import { useDictionary } from '@/store/modules/dictionary';
import dayjs from 'dayjs';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { useActivity } from './hooks/useActivity';
import SensitiveCheckModal from '@/views/components/sensitive/SensitiveCheckModal.vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { useActivityData } from './hooks/useActivityData';
import { useActivitySetting } from './hooks/useActivitySetting';
import { useActivitySubmit } from './hooks/useActivitySubmit';
import { useTemplateUtils } from './hooks/useTemplateUtils';
import { useFormSubmit } from './hooks/useFormSubmit';
import { useDataProcessors } from './hooks/useDataProcessors';
import { useActivityType } from './hooks/useActivityType';
import { useActivityTypeOperations } from './hooks/useActivityTypeOperations';
import CompanySelectModal from "@/views/components/company-select/company-select-modal.vue";

// 使用简化的数据处理器
const {
  safeCreateTimeArray,
  safeDateArray,
  processWalkingInfo,
  processSignUpInfo,
  processLotteryInfo,
  processVoteInfo,
} = useDataProcessors();

const TabPane = Tabs.TabPane;

const emit = defineEmits(['register', 'success', 'cancel']);
defineProps(p);
const dictionary = useDictionary();

const bigActivityType = inject<string | undefined>('bigActivityType', undefined);

// 使用活动类型管理 hook
const {
  activityType,
  computedActivityType,
  setActivityType,
  getCurrentActivityType,
  shouldShowPluginSelector,
  changeActivityType,
} = useActivityType();

// 使用活动类型操作管理 hook
const {
  initializeActivityTypeData,
  processActivityTabsForUpdate,
  handleOtherActivityParams,
  handleIfQuizChange,
  handleOtherActivityTabs,
  getActivityTypeDefaults,
} = useActivityTypeOperations();

// 使用 hooks
const {
  activeKey,
  allActiveKey,
  isUpdate,
  autoId,
  activityId,
  disabled,
  ifQuiz,
  otherTabs,
  addTitle,
  ifFilter,
  vieAnswerInfo,
  signUpInfo,
  luckDrawInfo,
  questionnaireInfo,
  voteInfo,
  coupon,
  educationAidInfo,
  walkInfo,
  record,
  title,
} = useActivityData();

const { resetActivitySetting, setActivityConfigData } = useActivitySetting();
const { checkIntegralConfig } = useActivitySubmit();

// 模板辅助工具
const { createTabVisibilityChecker } = useTemplateUtils();
const tabVisibility = createTabVisibilityChecker();

// 表单提交处理
const { handleFormSubmit, performSensitiveCheck } = useFormSubmit();

const otherOptions = ref<DefaultOptionType[]>([]);

const defaultCurrent = computed(() => {
  return dictionary.getDictionaryOpt.get('defaultCurrent') as RadioGroupChildOption[];
});

const activityTypeConfig = computed(() => {
  return [
    { value: ActivityType.QUIZ, label: ActivitySettingZh[ActivityType.QUIZ] },
    { value: ActivityType.SIGNUP, label: ActivitySettingZh[ActivityType.SIGNUP] },
  ];
});

// 计算的标签页可见性
const computedTabVisibility = computed(() => {
  const currentActivityType = getCurrentActivityType();
  const currentIfQuiz = unref(ifQuiz);
  const currentOtherTabs = unref(otherTabs);
  const currentDisabled = unref(disabled);

  return {
    showQuizTab: tabVisibility.isQuizTabVisible(currentIfQuiz, currentActivityType),
    showSignupTab: tabVisibility.isSignupTabVisible(currentIfQuiz, currentActivityType),
    showWalkTab: tabVisibility.isWalkTabVisible(currentIfQuiz, currentActivityType),
    showLotteryTab: tabVisibility.isLotteryTabVisible(
      currentIfQuiz,
      currentActivityType,
      currentOtherTabs
    ),
    showSurveyTab: tabVisibility.isSurveyTabVisible(
      currentIfQuiz,
      currentActivityType,
      currentOtherTabs
    ),
    showVoteTab: tabVisibility.isVoteTabVisible(currentIfQuiz, currentActivityType),
    showInclusiveTab: tabVisibility.isInclusiveTabVisible(currentIfQuiz, currentActivityType),
    showCouponTab: tabVisibility.isCouponTabVisible(currentIfQuiz, currentActivityType),
    showPluginSelector: tabVisibility.isPluginSelectorVisible(currentActivityType, currentDisabled),
    lotteryTabTitle: tabVisibility.getLotteryTabTitle(currentActivityType),
    lotteryActivityType: tabVisibility.getLotteryActivityType(currentActivityType),
  };
});

const [registerCompanySelectModal, { openModal:openCompanySelect }] = useModal();

//敏感词
const [registerSensitive, { openModal: openSensitive }] = useModal();
//注册基本详情
const [registerInfo, { validate, reset, setProps, setValues, resetDefaultField }] = useInfo();

//注册9大活动

const [
  registerQuiz,
  registerSignUp,
  registerLottery,
  registerSurvey,
  registerVote,
  registerInclusiveYJWD,
  registerInclusiveTicket,
  registerWalk,
  { validateQuiz, resetQuiz, setPropsQuiz, setValuesQuiz },
  { validateSignUp, resetSignUp, setPropsSignUp, setValuesSignUp },
  {
    validateLottery,
    resetLottery,
    setPropsLottery,
    setTableDataPrize,
    setValuesPrize,
    getDataSourcePrize,
  },
  { validateSurvey, resetSurvey, setPropsSurvey, setValuesSurvey },
  { validateVote, resetVote, setPropsVote, setValuesVote },
  { validateInclusiveYJWD, resetInclusiveYJWD, setPropsInclusiveYJWD, setValuesInclusiveYJWD },
  {
    validateInclusiveTicket,
    resetInclusiveTicket,
    setPropsInclusiveTicket,
    setValuesInclusiveTicket,
  },
  {
    validateWalk,
    resetWalk,
    setPropsWalk,
    setValuesWalk,
    setWalkTableDataPrize,
    getDataSourceWalkPrize,
  },
] = useActivity();

//注册 modal
const [registerModal, { setModalProps }] = useModalInner(async data => {
  try {
    //重置部分值
    await reset();

    await setActivityType(data.activityType || ActivityType.QUIZ);

    addTitle.value = data.addTitle;

    otherTabs.value = [];

    ifQuiz.value = '';

    autoId.value = undefined;

    activityId.value = '';

    ifFilter.value = true;
    //竞答
    vieAnswerInfo.value = {};
    //报名
    signUpInfo.value = {};
    //调查
    questionnaireInfo.value = {};
    //投票
    voteInfo.value = {};
    //抽奖
    luckDrawInfo.value = {};
    //普惠票券
    coupon.value = {};
    //金秋助学
    educationAidInfo.value = {};

    // 使用新的 hook 函数初始化活动类型数据
    const { otherOptions: newOtherOptions } = initializeActivityTypeData(
      getCurrentActivityType(),
      dictionary,
      bigActivityType || ''
    );
    otherOptions.value = newOtherOptions;

    disabled.value = !!data?.disabled;

    isUpdate.value = !!data?.isUpdate;
    record.value = {};

    setModalProps({
      confirmLoading: false,
      showOkBtn: !data.disabled,
    });
    if (unref(isUpdate)) {
      //注册原始值
      record.value = unref(data.record);
      const dataRecord = unref(data.record);

      const {
        activityId: id,
        autoId: aId,
        vieAnswerInfo: vieAnswer,
        signUpInfo: registration,
        questionnaireInfo: questionnaire,
        voteInfo: vote,
        luckDrawInfo: luckDraw,
        walkingInfo,
        areaCode,
        openingStartTime,
        openingEndTime,
        activityStartTime,
        activityEndTime,
        educationAidInfo: education,
        couponExtend,
      } = dataRecord;

      // 使用新的 hook 函数处理活动Tab逻辑
      const { ifQuizValue } = processActivityTabsForUpdate(
        getCurrentActivityType(),
        dataRecord,
        otherTabs,
        allActiveKey
      );
      ifQuiz.value = ifQuizValue;

      //使用数据处理函数
      vieAnswerInfo.value = vieAnswer || {};
      signUpInfo.value = registration ? processSignUpInfo(registration) : {};
      questionnaireInfo.value = questionnaire || {};
      voteInfo.value = vote ? processVoteInfo(vote) : {};
      luckDrawInfo.value = luckDraw ? processLotteryInfo(luckDraw) : {};
      coupon.value = couponExtend || {};
      walkInfo.value = walkingInfo ? processWalkingInfo(walkingInfo) : {};
      educationAidInfo.value = education || undefined;

      //反填时间
      const dailyTime = safeCreateTimeArray(openingStartTime, openingEndTime);
      await setValues({
        ...unref(data.record),
        ifQuiz: unref(ifQuiz),
        areaCode: areaCode ? areaCode?.split(',') : undefined,
        dailyTime: dailyTime as any,
        startEndDate: safeDateArray(activityStartTime, activityEndTime),
        filePath: unref(educationAidInfo) ? [unref(educationAidInfo).filePath] : undefined,
        expiredDateTime: unref(educationAidInfo)
          ? unref(educationAidInfo).expiredDateTime
          : dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      activityId.value = id;

      autoId.value = aId;

      if (!unref(educationAidInfo) && unref(ifQuiz) === '1') {
        // 确保在下一个tick中执行，让所有组件完全初始化
        await nextTick();
        await setFromSetting(getCurrentActivityType());
      }
    } else {
      // 使用新的 hook 函数获取默认配置
      const defaults = getActivityTypeDefaults(getCurrentActivityType());
      setValues(defaults);
    }

    setProps({ activityType: getCurrentActivityType(), disabled: unref(disabled) });
  } catch (error) {
    console.error('Error in modal initialization:', error);
    // 确保在出错时也设置基本props
    setProps({ activityType: getCurrentActivityType(), disabled: unref(disabled) });
  }
});

//取消
async function handleCancel() {
  if (unref(otherTabs)?.length > 0 || unref(ifQuiz) === '1') {
    await handleReset();
  }

  activeKey.value = 'activity';
  allActiveKey.value = ['activity'];
}

//提交
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    // 简化的提交配置对象
    const submitConfig = {
      activityType,
      validators: {
        validate,
        validateQuiz,
        validateSignUp,
        validateLottery,
        validateSurvey,
        validateVote,
        validateInclusiveYJWD,
        validateInclusiveTicket,
        validateWalk,
      },
      dataSources: { getDataSourcePrize, getDataSourceWalkPrize },
      activityData: {
        vieAnswerInfo,
        signUpInfo,
        luckDrawInfo,
        questionnaireInfo,
        voteInfo,
        coupon,
        record,
        isUpdate,
        autoId,
        activityId,
        ifQuiz,
        educationAidInfo,
        walkInfo,
      },
      otherParams,
    };

    const params = await handleFormSubmit(submitConfig);

    if (!unref(ifFilter)) {
      emit('success', { params });
      handleCancel();
    } else {
      const { needsReview, sensitiveData } = await performSensitiveCheck(params);

      if (needsReview) {
        openSensitive(true, { data: sensitiveData });
      } else {
        await checkIntegralConfig(params, emit, handleCancel);
      }
    }
  } catch (error) {
    const { errorFields } = error as Recordable;
    if (errorFields?.length > 0) {
      console.error(error)
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `请检查活动信息是否填写正确`,
        okText: '确认',
        closable: true,
      });
    } else {
      throw error;
    }
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

async function otherParams(flg?: boolean) {
  // 使用新的 hook 函数处理其他活动参数
  return await handleOtherActivityParams(
    getCurrentActivityType(),
    allActiveKey,
    activityId,
    luckDrawInfo,
    questionnaireInfo,
    {
      validateLottery,
      validateSurvey,
    },
    {
      resetLottery,
      resetSurvey,
      setTableDataPrize,
      setValuesSurvey,
    },
    getDataSourcePrize,
    !!flg
  );
}

//切换tabs
async function changeTabs(active) {
  activeKey.value = active;

  // 如果切换到的是配置Tab且是更新模式，确保数据已正确加载
  if (active !== 'activity' && unref(isUpdate) && unref(ifQuiz) === '1') {
    await nextTick();
    await setFromSetting(active);
  }
}
//选择参与对象
const onChangeCustomerType = (m, f, e) => {

  //m[f] = e;
  m['extendList'] = undefined;
  m['extendIds'] = undefined;
  m['checkDate'] = '';
};

//选择指定工会
function handleChooseUnion(type) {
  openCompanySelect(true, {});
}

function handleCompanySelectSuccess({ companyId, companyName, record }){
  setValues({extendList:[{sourceId:companyId,sourceName:companyName}]},true)
}

function deleteUnion(m,f,item){
  m[f] = m[f].filter(t=>t.sourceId!==item.sourceId);
  setValues({extendList: m[f] },true)
}

//change - 处理ifQuiz变化
async function ifQuizChange(change: { target: any }) {
  const { target } = change;

  // 使用新的 hook 函数处理ifQuiz变化
  const { shouldResetSettings } = handleIfQuizChange(
    target.value,
    getCurrentActivityType(),
    ifQuiz,
    allActiveKey
  );

  // 如果从禁用状态切换到启用状态，需要重新设置数据
  if (shouldResetSettings && unref(isUpdate)) {
    await nextTick();
    await setFromSetting(getCurrentActivityType());
  }
}

//其他活动 - 处理插件Tab变化
async function handleOthers(value) {
  // 使用新的 hook 函数处理其他活动Tab
  await handleOtherActivityTabs(
    value,
    otherTabs,
    allActiveKey,
    unref(isUpdate),
    unref(disabled),
    {
      luckDrawInfo,
      questionnaireInfo,
    },
    {
      setPropsLottery,
      setPropsSurvey,
      setValuesPrize,
      setValuesSurvey,
      setTableDataPrize,
    }
  );
}

async function handleReset() {
  try {
    // 使用 resetActivitySetting hook 函数
    await resetActivitySetting(getCurrentActivityType(), {
      resetQuiz,
      resetSignUp,
      resetLottery,
      resetSurvey,
      resetVote,
      resetInclusiveYJWD,
      resetInclusiveTicket,
      resetWalk,
      setValuesQuiz,
      setValuesSignUp,
      setTableDataPrize,
      setValuesSurvey,
      setValuesVote,
      setValuesInclusiveYJWD,
      setWalkTableDataPrize,
    });

    await otherParams(true);
  } catch (error) {
    throw new Error(`Reset error for activity type ${getCurrentActivityType()}: ${error}`);
  }
}

async function setFromSetting(key) {
  await nextTick();

  try {
    const disabledValue = unref(disabled);

    // 使用 setActivityConfigData hook 函数
    await setActivityConfigData(
      key,
      disabledValue,
      {
        vieAnswerInfo,
        signUpInfo,
        luckDrawInfo,
        questionnaireInfo,
        voteInfo,
        coupon,
        walkInfo,
        allActiveKey,
      },
      {
        setPropsQuiz,
        setPropsSignUp,
        setPropsLottery,
        setPropsSurvey,
        setPropsVote,
        setPropsInclusiveYJWD,
        setPropsInclusiveTicket,
        setPropsWalk,
        setValuesQuiz,
        setValuesSignUp,
        setValuesPrize,
        setValuesSurvey,
        setValuesVote,
        setValuesInclusiveYJWD,
        setValuesInclusiveTicket,
        setValuesWalk,
        setTableDataPrize,
        setWalkTableDataPrize,
      }
    );
  } catch (error) {
    console.warn('Error in setFromSetting:', error, { key, disabled: unref(disabled) });
  }
}

function handleCheck({ filter }) {
  ifFilter.value = filter;
  handleSubmit();
}
</script>

<style lang="less" module>
.activity-modal {
  :global {
    .scroll-container {
      .scrollbar__view {
        .ant-upload-list {
          display: none;
        }

        .question-list {
          .ant-form-item-control {
            padding: 10px;
            border: 1px solid rgb(190, 190, 190);
          }
        }
      }
    }

    .ant-collapse-content-box,
    .ant-collapse-header {
      padding: 5px !important;
    }

    .dy-prize-table {
      .ant-table-body {
        height: unset !important;
        max-height: unset !important;
      }
    }

    .user-form-select {
      width: calc(100% - 80px);
    }

    .label-center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .danger-question-list {
      span {
        margin: 0 !important;
      }
    }

    .icon-class-right {
      display: flex;
      justify-content: right;
      align-items: center;

      .app-iconify {
        cursor: pointer;
      }
    }

    .up-down {
      display: inline-grid;
      justify-content: center;
      align-items: center;
    }

    .icon-red {
      color: rgb(230, 62, 62);
    }

    .icon-gray {
      color: gray;
      justify-content: center;
    }

    .ant-image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
