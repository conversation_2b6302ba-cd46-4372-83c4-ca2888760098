import { unref } from 'vue';
import { map, find } from 'lodash-es';
import { useMessage } from '@monorepo-yysz/hooks';

/**
 * 新闻内容提交管理 Hook
 * 集成了内容复制、验证、提交等功能
 */
export function useNewsSubmission() {
  const { createMessage } = useMessage();

  /**
   * 处理一键复制功能
   */
  const handleCopyContent = (activeTabs: any, tabsPane: any) => {
    return new Promise(resolve => {
      const activeNode = find(
        unref(tabsPane),
        v => v.platformType === unref(activeTabs)
      ) as Recordable;

      if (!unref(activeTabs)) {
        resolve(false);
        return;
      }

      const {
        newsDetailsTitle,
        newsDetailsContent,
        fileName,
        fileAddress,
        newsDetailsAbstract,
        newsCoverUrl,
        fileList,
        externalLinkAddress,
      } = activeNode;

      tabsPane.value = map(unref(tabsPane), v => {
        if (v.platformType !== unref(activeTabs)) {
          return {
            ...v,
            newsDetailsTitle,
            newsDetailsContent,
            fileName,
            fileAddress,
            newsDetailsAbstract,
            newsCoverUrl,
            fileList,
            externalLinkAddress,
          };
        }
        return { ...v };
      }) as Recordable[];

      resolve(true);
    }).then(res => {
      if (res) {
        createMessage.success('复制成功');
      } else {
        createMessage.error('复制失败，当前请填写当前页面内容！');
      }
    });
  };

  /**
   * 处理外链切换
   */
  const handleExternalLinkChange = (
    checked: boolean,
    field: string,
    isUrl: any,
    tabsPane: any,
    dictionary: any
  ) => {
    if (field === 'whetherExternalLink') {
      isUrl.value = checked as boolean;

      tabsPane.value = map(unref(tabsPane), v => {
        const type = v.platformType;
        return {
          title: dictionary.getDictionaryMap.get(`appType_${type}`)?.dictName,
          platformType: type + '',
          key: `appType_${type}`,
        } as Recordable;
      });
    }
  };

  /**
   * 处理封面图数据
   * 将多个封面图字段合并为单一字段，并处理布尔值转换
   */
  const processCoverImages = (newsDetailsList: Recordable[]): Recordable[] => {
    for (let i = 0; i < newsDetailsList.length; i++) {
      let newsCoverUrl: string[] = [];

      if (newsDetailsList[i].newsCoverUrl1) {
        newsCoverUrl = newsCoverUrl.concat(newsDetailsList[i].newsCoverUrl1);
      }
      if (newsDetailsList[i].newsCoverUrl2) {
        newsCoverUrl = newsCoverUrl.concat(newsDetailsList[i].newsCoverUrl2);
      }
      if (newsDetailsList[i].newsCoverUrl3) {
        newsCoverUrl = newsCoverUrl.concat(newsDetailsList[i].newsCoverUrl3);
      }

      if (newsCoverUrl && newsCoverUrl.length > 0) {
        newsDetailsList[i].newsCoverUrl = newsCoverUrl.join(',');
      }

      newsDetailsList[i].whetherLinkResources = newsDetailsList[i].whetherLinkResources
        ? true
        : false;
      newsDetailsList[i].detailsWhetherPrompt = newsDetailsList[i].detailsWhetherPrompt
        ? true
        : false;
    }

    return newsDetailsList;
  };

  /**
   * 处理单个新闻项的封面图
   */
  const processSingleItemCoverImages = (newsItem: Recordable): Recordable => {
    let newsCoverUrl: string[] = [];

    if (newsItem.newsCoverUrl1) {
      newsCoverUrl = newsCoverUrl.concat(newsItem.newsCoverUrl1);
    }
    if (newsItem.newsCoverUrl2) {
      newsCoverUrl = newsCoverUrl.concat(newsItem.newsCoverUrl2);
    }
    if (newsItem.newsCoverUrl3) {
      newsCoverUrl = newsCoverUrl.concat(newsItem.newsCoverUrl3);
    }

    if (newsCoverUrl && newsCoverUrl.length > 0) {
      newsItem.newsCoverUrl = newsCoverUrl.join(',');
    }

    newsItem.whetherLinkResources = newsItem.whetherLinkResources ? true : false;
    newsItem.detailsWhetherPrompt = newsItem.detailsWhetherPrompt ? true : false;

    return newsItem;
  };

  /**
   * 验证新闻内容
   */
  const validateNewsContent = (
    tabsPane: any,
    isUrl: any,
    appCoverWhether: any,
    gwCoverWhether: any,
    appMaxCoverNumber: any,
    gwMaxCoverNumber: any,
    isUploadFiles: any
  ) => {
    return map(unref(tabsPane), v => {
      // 处理封面图
      let newsCoverUrl: string[] = [];
      if (v.newsCoverUrl1) {
        newsCoverUrl = newsCoverUrl.concat(v.newsCoverUrl1);
      }
      if (v.newsCoverUrl2) {
        newsCoverUrl = newsCoverUrl.concat(v.newsCoverUrl2);
      }
      if (v.newsCoverUrl3) {
        newsCoverUrl = newsCoverUrl.concat(v.newsCoverUrl3);
      }
      v.newsCoverUrl = newsCoverUrl?.length > 0 ? newsCoverUrl.join(',') : undefined;

      if (unref(isUrl)) {
        if (!v.externalLinkAddress) {
          throw new Error('url');
        }
        if (!v.newsDetailsTitle) {
          throw new Error('title');
        }
      } else {
        // 工会APP端封面图验证
        if ('GHAPP' === v.platformType && 'Y' === unref(appCoverWhether) && !unref(isUploadFiles)) {
          if (!v.newsCoverUrl) {
            throw new Error('nullCover');
          }
          if (
            unref(appMaxCoverNumber) &&
            v.newsCoverUrl.split(',').length !== unref(appMaxCoverNumber)
          ) {
            throw new Error('appCoverNumber');
          }
        }

        // 工会官网端封面图验证
        if ('GHGW' === v.platformType && 'Y' === unref(gwCoverWhether) && !unref(isUploadFiles)) {
          if (!v.newsCoverUrl) {
            throw new Error('nullCover');
          }
          if (
            unref(gwMaxCoverNumber) &&
            v.newsCoverUrl.split(',').length !== unref(gwMaxCoverNumber)
          ) {
            throw new Error('gwCoverNumber');
          }
        }

        // 标题验证
        if (!v.newsDetailsTitle && !unref(isUploadFiles)) {
          throw new Error('title');
        }

        // 摘要验证
        if ('Y' === v.abstractWhether && !v.newsDetailsAbstract && !unref(isUploadFiles)) {
          throw new Error('detailsAbstract');
        }

        // 内容验证
        if (!v.newsDetailsContent && !unref(isUploadFiles)) {
          throw new Error('content');
        }

        // 上传文件模式验证
        if (!v.newsDetailsTitle && unref(isUploadFiles)) {
          throw new Error('title');
        }
        if (!v.fileAddress && unref(isUploadFiles)) {
          throw new Error('file');
        }
      }

      return { ...v };
    });
  };

  /**
   * 处理验证错误消息
   */
  const handleValidationError = (
    error: any,
    name: string,
    appMaxCoverNumber: any,
    gwMaxCoverNumber: any
  ) => {
    const errorMap = {
      nullCover: `${name}中封面图不能为空`,
      appCoverNumber: `${name}中封面图必须上传${unref(appMaxCoverNumber)}张`,
      gwCoverNumber: `${name}中封面图必须上传${unref(gwMaxCoverNumber)}张`,
      title: `${name}中资讯标题不能为空`,
      detailsAbstract: `${name}中资讯摘要不能为空`,
      content: `${name}中内容不能为空`,
      url: `${name}中外链地址不能为空`,
      file: `${name}中文件不能为空`,
    };

    const message = errorMap[error.message] || '验证失败';
    createMessage.warning(message);
    throw error;
  };

  /**
   * 准备提交数据
   * 整合所有处理步骤
   */
  const prepareSubmissionData = (
    tabsPane: any,
    options: {
      isUrl?: any;
      appCoverWhether?: any;
      gwCoverWhether?: any;
      appMaxCoverNumber?: any;
      gwMaxCoverNumber?: any;
      isUploadFiles?: any;
    } = {}
  ) => {
    try {
      // 先处理封面图数据
      const processedData = processCoverImages(unref(tabsPane));

      // 然后进行验证
      const validatedData = validateNewsContent(
        { value: processedData },
        options.isUrl,
        options.appCoverWhether,
        options.gwCoverWhether,
        options.appMaxCoverNumber,
        options.gwMaxCoverNumber,
        options.isUploadFiles
      );

      return validatedData;
    } catch (error) {
      // 这里可以添加错误处理逻辑
      throw error;
    }
  };

  return {
    handleCopyContent,
    handleExternalLinkChange,
    processCoverImages,
    processSingleItemCoverImages,
    validateNewsContent,
    handleValidationError,
    prepareSubmissionData,
  };
}
