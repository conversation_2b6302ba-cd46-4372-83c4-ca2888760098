<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrap-class-name="`full-modal-self ${$style['chanel--news-modal']}`"
    ok-text="即时发布"
    :okButtonProps="{ auth: '/channelNews/timelyPublish' }"
    :showOtherBtn="true"
    otherText="提交审核"
    @other="handleOther"
    :otherButtonProps="{ auth: '/channelNews/submitAudit' }"
    :show-view-btn="true"
    view-text="保存草稿"
    @view="handleDraft"
  >
    <!--    view-text="即时预览"
    @view="handleView" -->
    <a-row>
      <a-col
        :span="8"
        class="overflow-auto max-h-79vh"
      >
        <a-collapse v-model:activeKey="activeKey">
          <a-collapse-panel
            key="activeKey-1"
            header="基础信息"
          >
            <div v-if="categoryId.length > 0 && categoryType === 'UNIVERSAL'">
              <label class="!w-1/2">
                <span
                  class="text-red-600"
                  v-if="
                    ('GHAPP' === activeTabs && 'Y' === appCoverWhether) ||
                    ('GHGW' === activeTabs && 'Y' === gwCoverWhether)
                  "
                  >*</span
                >
                {{
                  `${dictionary.getDictionaryMap.get(`appType_${activeTabs}`)?.dictName || ''}封面图`
                }}<span class="text-red-500">{{ imageSizeName }}</span>
                <span
                  class="text-red-500"
                  v-if="'GHAPP' === activeTabs && 'Y' === appCoverWhether && appMaxCoverNumber"
                  >{{ `,必须上传${appMaxCoverNumber}张` }}</span
                >
                <span
                  class="text-red-500"
                  v-if="'GHGW' === activeTabs && 'Y' === gwCoverWhether && gwMaxCoverNumber"
                  >{{ `,必须上传${gwMaxCoverNumber}张` }}</span
                >
              </label>
              <div
                class="flex"
                v-show="activeTabs === item.platformType"
                v-for="item in tabsPane"
              >
                <!--封面图1-->
                <div class="!w-1/2">
                  <CropperForm
                    :value="item.newsCoverUrl1"
                    @change="e => (item.newsCoverUrl1 = e)"
                    :operateType="19"
                    :imgSize="imageSize"
                  />
                </div>
                <!--封面图2-->
                <div class="!w-1/2">
                  <CropperForm
                    :value="item.newsCoverUrl2"
                    @change="e => (item.newsCoverUrl2 = e)"
                    :operateType="19"
                    :imgSize="imageSize"
                  />
                </div>
                <!--封面图3-->
                <div class="!w-1/2">
                  <CropperForm
                    :value="item.newsCoverUrl3"
                    @change="e => (item.newsCoverUrl3 = e)"
                    :operateType="19"
                    :imgSize="imageSize"
                  />
                </div>
              </div>
            </div>
            <BasicForm @register="registerForm">
              <template #outsideurl="{ model, field }">
                <a-switch
                  v-model:checked="model[field]"
                  checked-children="开"
                  :checkedValue="true"
                  @change="checked => handleChangeFormItem(checked, field)"
                />
              </template>
            </BasicForm>
          </a-collapse-panel>
          <a-collapse-panel
            key="activeKey-2"
            header="栏目"
            v-if="!ifCategoryId"
          >
            <!-- 栏目 -->
            <ApiTree
              v-model:value="categoryId"
              :api="getUnionTree"
              :params="{ tenantChildFlag: 1 }"
              :showLine="true"
              :showIcon="true"
              v-model:expandedKeys="expand"
              :autoExpandParent="true"
              @expand="handleExpand"
              :fieldNames="{ title: 'categoryName', key: 'categoryId', children: 'children' }"
              @select="handleChangeTree"
            />
          </a-collapse-panel>
        </a-collapse>
      </a-col>
      <a-col
        :span="16"
        class="p-10px overflow-y-auto overflow-x-hidden max-h-79vh"
      >
        <a-typography>
          <a-typography-title
            :level="3"
            class="font-sans"
          >
            {{ parentAndChildName }}
          </a-typography-title>
        </a-typography>

        <a-tabs
          :active-key="activeTabs"
          @change="handelTabs"
          type="card"
          size="large"
          class="basic-border rounded-1"
          v-if="tabsPane?.length > 0"
        >
          <a-tab-pane
            v-for="item in tabsPane"
            :key="item.platformType"
            :tab="item.title"
          >
            <ChanelContent
              :is-url="isUrl"
              :item="item"
              :is-update="isUpdate"
              :is-upload-files="isUploadFiles"
              :record="record"
              @get-tags="tags => (item.tags = tags)"
            />
          </a-tab-pane>
        </a-tabs>
        <a-empty
          description="请先选择栏目信息"
          class="justify-center h-97/100 w-full items-center flex"
          :image="finger"
          v-else
        />
      </a-col>
    </a-row>
    <template
      #insertFooter
      v-if="tabsPane?.length > 1"
    >
      <a-button
        @click="handleCopy"
        type="primary"
        >一键复制
      </a-button>
    </template>
    <SensitiveCheckModal
      @register="registerSensitive"
      @success="handleCheck"
      :can-fullscreen="false"
      width="30%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, unref, watch } from 'vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm, ApiTree } from '@/components/Form';
import finger from '@/assets/images/finger.png';
import { useDictionary } from '@/store/modules/dictionary';
import { getUnionTree } from '@/api/category';
import { modalFormSchema } from './data';
import { useUserStore } from '@/store/modules/user';
import { CropperForm } from '@/components/Cropper';
import SensitiveCheckModal from '@/views/components/sensitive/SensitiveCheckModal.vue';
import { useNewsModal } from './hooks/useNewsModal';
import { useCategoryOperations } from './hooks/useCategoryOperations';
import { useNewsSubmit } from './hooks/useNewsSubmit';
import { useNewsSubmission } from './components/hooks/useNewsSubmission';
import { useTreeOperations } from './hooks/useTreeOperations';
import ChanelContent from './components/ChanelContent.vue';
import { isEmpty } from 'lodash-es';

const emit = defineEmits(['success', 'register', 'cancel', 'audit', 'view']);

const dictionary = useDictionary();
const userStore = useUserStore();

// 使用新闻模态框状态管理
const {
  isUpdate,
  autoId,
  record,
  addType,
  ifFilter,
  categoryId,
  category,
  categoryType,
  categoryPlatformType,
  parentAndChildName,
  appCoverWhether,
  appMaxCoverNumber,
  gwCoverWhether,
  gwMaxCoverNumber,
  categorySortOrder,
  imageSize,
  imageSizeName,
  isUrl,
  tabsPane,
  detailList,
  activeKey,
  columnList,
  activeTabs,
  expand,
  isUploadFiles,
  ifCategoryId,
  title,
  categoryInfo,
  resetStates,
  initEditData,
  initAddData,
} = useNewsModal();

// 使用栏目操作管理
const { handleCategoryChange: performCategoryChange, handleTabChange: performTabChange } =
  useCategoryOperations();

// 使用新闻内容和提交管理
const { handleCopyContent, handleExternalLinkChange } = useNewsSubmission();

// 使用新闻提交管理
const { handleSubmitAudit, handleSaveDraft, handleTimelyPublish, handleSensitiveCheckComplete } =
  useNewsSubmit();

// 使用树操作管理
const { handleTreeChange, handleTreeExpand, updateFormDisplay } = useTreeOperations();

const form = computed(() => {
  return modalFormSchema(
    unref(categorySortOrder),
    unref(categoryType),
    unref(categoryPlatformType)
  );
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await reset();

  isUpdate.value = !!data.isUpdate;
  record.value = data.record;

  if (unref(isUpdate)) {
    const editData = initEditData(data.record);
    performCategoryChange(
      data.record.categoryInfo?.platformType || '',
      data.record.categoryInfo,
      dictionary,
      tabsPane,
      detailList,
      activeTabs,
      category,
      appCoverWhether,
      appMaxCoverNumber,
      gwCoverWhether,
      gwMaxCoverNumber,
      categorySortOrder,
      imageSize,
      imageSizeName,
      isUpdate,
      setFieldsValue
    );
    setFieldsValue(editData);
    console.log(tabsPane);
  } else {
    const addData = initAddData();
    setFieldsValue(addData);

    if (!isEmpty(categoryInfo)) {
      categoryId.value = [unref(categoryInfo).categoryId];
      handleChangeTree(null, { node: unref(categoryInfo) });
    }
  }

  setModalProps({ confirmLoading: false });
});

async function reset() {
  await resetFields();
  resetStates();
}
const [registerSensitive, { openModal: openSensitive, closeModal }] = useModal();

const [registerForm, { validate, resetFields, setFieldsValue, updateSchema }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
});

// tree change
async function handleChangeTree(_, event) {
  await handleTreeChange(
    _,
    event,
    categoryType,
    categoryPlatformType,
    autoId,
    (platformType, categoryInfo) =>
      performCategoryChange(
        platformType,
        categoryInfo,
        dictionary,
        tabsPane,
        detailList,
        activeTabs,
        category,
        appCoverWhether,
        appMaxCoverNumber,
        gwCoverWhether,
        gwMaxCoverNumber,
        categorySortOrder,
        imageSize,
        imageSizeName,
        isUpdate,
        setFieldsValue
      )
  );
}

// handelTabs 切换
function handelTabs(key) {
  performTabChange(key, category, activeTabs, imageSize, imageSizeName);
}

function handleExpand(expandedKeys) {
  handleTreeExpand(expandedKeys, expand, columnList);
}

// copy
function handleCopy() {
  handleCopyContent(activeTabs, tabsPane);
}

// 切换url
function handleChangeFormItem(checked, field) {
  handleExternalLinkChange(checked, field, isUrl, tabsPane, dictionary);
}

// 提交审核
async function handleOther() {
  await handleSubmitAudit(
    addType,
    validate,
    tabsPane,
    categoryId,
    autoId,
    record,
    ifFilter,
    userStore,
    openSensitive,
    emit,
    isUpdate
  );
}
// 保存草稿
async function handleDraft() {
  await handleSaveDraft(
    addType,
    validate,
    tabsPane,
    categoryId,
    autoId,
    record,
    ifFilter,
    userStore,
    openSensitive,
    emit,
    isUpdate
  );
}

// 及时发布
async function handleSubmit() {
  await handleTimelyPublish(
    addType,
    validate,
    tabsPane,
    categoryId,
    autoId,
    record,
    ifFilter,
    userStore,
    openSensitive,
    emit,
    isUpdate
  );
}

function handleCheck({ filter }) {
  handleSensitiveCheckComplete(filter, addType, ifFilter, closeModal, handleSubmit, handleOther);
}

watch(isUploadFiles, () => {
  updateFormDisplay(isUploadFiles, updateSchema);
});
</script>

<style module lang="less">
.chanel--news-modal {
  :global {
    .ant-collapse-header {
      background-color: @primary-color;
      color: #ffffff !important;
    }

    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
