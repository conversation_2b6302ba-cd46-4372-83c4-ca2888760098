import { nextTick, onUnmounted, ref, unref, watch } from 'vue';
import { isProdMode } from '@/utils/env';
import { error } from '@/utils/log';
import { getDynamicProps } from '@monorepo-yysz/utils';
import { ActivityType } from '../../activities';
import { DynamicProps } from '@monorepo-yysz/types';
import type { NamePath } from 'ant-design-vue/lib/form/interface';

export interface InfoActionProps {
  disabled?: boolean;
  activityType: ActivityType;
  values?: any;
}

export type Props = Partial<DynamicProps<InfoActionProps>>;

export interface InfoActionType {
  validate: () => Promise<any>;
  reset: () => Promise<void>;
  setProps: (props: Partial<InfoActionProps>) => Promise<void>;
  setValues: <T>(values: T,flag?:boolean) => Promise<void>;
  resetDefaultField: (nameList?: NamePath[]) => Promise<void>;
}

export type RegisterFn = (domInstance: InfoActionType) => void;

export type UseInfoReturnType = [RegisterFn, InfoActionType];

export function useInfo(props?: Props): UseInfoReturnType {
  const domRef = ref<Nullable<InfoActionType>>(null);
  const loadedRef = ref<Nullable<boolean>>(false);

  async function getDom() {
    const dom = unref(domRef);
    if (!dom) {
      error('no dom!');
    }
    await nextTick();
    return dom as InfoActionType;
  }

  function register(instance: InfoActionType) {
    isProdMode() &&
      onUnmounted(() => {
        domRef.value = null;
        loadedRef.value = null;
      });
    if (unref(loadedRef) && isProdMode() && instance === unref(domRef)) return;

    domRef.value = instance;
    loadedRef.value = true;

    watch(
      () => props,
      () => {
        props && instance.setProps(getDynamicProps(props));
      },
      {
        immediate: true,
        deep: true,
      }
    );
  }

  const methods: InfoActionType = {
    validate: async (): Promise<Recordable> => {
      const dom = await getDom();
      return dom.validate();
    },
    reset: async () => {
      getDom().then(async dom => {
        await dom.reset();
      });
    },
    setProps: async (props: Partial<InfoActionProps>) => {
      const dom = await getDom();
      dom.setProps(props);
    },
    setValues: async <T>(values: T,flag?:boolean) => {
      const dom = await getDom();
      dom.setValues<T>(values,flag);
    },
    resetDefaultField: async (nameList?: NamePath[]) => {
      const dom = await getDom();
      if (dom) {
        dom.resetDefaultField(nameList);
      } else {
        error('no dom to reset default field');
      }
    },
  };
  return [register, methods];
}
