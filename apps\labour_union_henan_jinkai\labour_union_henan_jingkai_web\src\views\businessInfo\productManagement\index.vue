<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增积分商品
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                disabled: record.state !== 'down',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'carbon:cut-out',
                label: '上架',
                disabled: record.state !== 'down',
                type: 'primary',
                onClick: handleUpProduct.bind(null, record),
              },
              {
                icon: 'bx:log-out-circle',
                label: '下架',
                type: 'primary',
                danger: true,
                onClick: handleDownProduct.bind(null, record),
                auth: '/productManagement/down',
              },
              {
                icon: 'tabler:list-details',
                label: '规格',
                type: 'primary',
                onClick: specificationManage.bind(null, record),
              },
              {
                icon: 'tabler:exchange',
                label: '兑换记录',
                type: 'primary',
                onClick: handleExchangeRecords.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ProductModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import ProductModal from './ProductModal.vue';
import {
  enableOrDisableProduct,
  getProductPriceInfoByProductId,
  list,
  view,
  saveApi,
  updateApi,
} from '@/api/productManagement';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

// 兑换记录
const [registerExchangeRecordsModal, { openModal: openExchangeRecordsModal }] = useModal();

// 注册规格商品列表弹框
const [registerCommentModal, { openModal: openSpecificationsModal }] = useModal();

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  view({ productId: record.productId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

// 详情
function handleView(record: Recordable<any>) {
  view({ productId: record.productId }).then(res => {
    if (res.code === 200) {
      record.productIntroduce = res.data.productIntroduce;
      openModal(true, {
        record,
        isUpdate: true,
        disabled: true,
      });
    }
  });
}

// 主商品上架接口
function handleUpProduct(record) {
  openSpecificationsModal(true, {
    record: record,
    isMain: true,
    isShow: false,
    canSubmit: false,
  });
}

// 主商品下架
function handleDownProduct(record) {
  const specificationsIds = [];
  getProductPriceInfoByProductId({ productId: record.productId }).then(res => {});

  createConfirm({
    title: '主商品下架时,所有规格也会被下架!',
    iconType: 'warning',
    content: `确定要下架${record.productName || ''}?`,
    onOk: function () {
      const downParams = {
        operateProductType: 'integral',
        productId: record.productId,
        operateType: 'down',
        productSubIdList: specificationsIds,
      };
      try {
        enableOrDisableProduct(downParams).then(res => {
          if (res.code === 200) {
            createSuccessModal({ content: '积分主商品下架成功!' });
          } else {
            createSuccessModal({ content: '积分主商品下架失败!' });
          }
          reload();
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

// 兑换记录
function handleExchangeRecords(record) {
  openExchangeRecordsModal(true, {
    record: record,
  });
}

// 查看商品规格管理操作
function specificationManage(record) {
  openSpecificationsModal(true, {
    record: record,
    isMain: false,
    isShow: true,
    canSubmit: false,
  });
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? updateApi : saveApi;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}
</script>
