<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :wrap-class-name="$style['product-info-modal']"
  >
    <a-tabs
      v-model:activeKey="activeTab"
      type="card"
    >
      <a-tab-pane
        key="basic"
        tab="基本信息"
      >
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
        />
      </a-tab-pane>

      <a-tab-pane
        key="specs"
        tab="规格信息"
      >
        <ProductSpecsList
          v-model:value="productSpecs"
          :disabled="disabled"
        />
      </a-tab-pane>
    </a-tabs>

    <CompaniesModal
      @register="registerCompanyModal"
      :canFullscreen="false"
      width="50%"
      @success="handleSuccess"
    />

    <CompaniesNextModal
      @register="registerCompanyNextModal"
      :canFullscreen="false"
      width="50%"
      @success="handleNextSuccess"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed, nextTick } from 'vue';
import { useModalInner, BasicModal, useModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import CompaniesModal from './CompaniesModal.vue';
import CompaniesNextModal from './CompaniesNextModal.vue';
import ProductSpecsList from './ProductSpecsList.vue';
import { join, split } from 'lodash-es';
import type { ProductSpec } from './types';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const productSpecs = ref<ProductSpec[]>([]);

const activeTab = ref<string>('basic');

// 添加一个标志来防止初始化时的响应式更新冲突
const isInitializing = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.productName || ''}--详情`
      : `编辑${unref(record)?.productName || ''}`
    : '新增积分商品';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(choiceCompany);
});

// 注册商户表单
const [registerCompanyModal, { openModal: openCompanyModal, closeModal: closeCompanyModal }] =
  useModal();

// 注册二级商户表单
const [
  registerCompanyNextModal,
  { openModal: openCompanyNextModal, closeModal: closeCompanyNextModal },
] = useModal();

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  isInitializing.value = true;

  try {
    await resetFields();

    record.value = data.record;
    disabled.value = !!data.disabled;
    isUpdate.value = !!data.isUpdate;

    // 使用 nextTick 避免响应式更新冲突
    await nextTick(() => {
      // 重置到基本信息标签页
      activeTab.value = 'basic';

      // 重置规格数据
      productSpecs.value = [];

      if (unref(isUpdate)) {
        setFieldsValue({
          ...data.record,
          productPublicityImg: data.record.productPublicityImg
            ? split(data.record.productPublicityImg, ',')
            : '',
        });

        // 如果有规格数据，设置规格列表
        if (data.record.productSpecs && Array.isArray(data.record.productSpecs)) {
          productSpecs.value = [...data.record.productSpecs];
        }
      }

      setProps({ disabled: unref(disabled) });
      setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
    });
  } finally {
    isInitializing.value = false;
  }
});

// 选择商户
function choiceCompany(typeFlag: boolean) {
  typeFlag ? openCompanyModal(true, {}) : openCompanyNextModal(true, {});
}

// 确认商户
async function handleSuccess({ record }) {
  await setFieldsValue({
    companyName: record.companyName,
    companyId: record.companyId,
  });
  closeCompanyModal();
}

// 确认二级商户
async function handleNextSuccess({ companyNames, companyIds }) {
  await setFieldsValue({
    childProductCompanyName: companyNames,
    childProductCompany: companyIds,
  });
  closeCompanyNextModal();
}

// 校验规格数据
function validateProductSpecs(specs: ProductSpec[]): string | null {
  if (!specs || specs.length === 0) {
    return '请至少添加一个商品规格';
  }

  for (let i = 0; i < specs.length; i++) {
    const spec = specs[i];
    const index = i + 1;

    if (!spec.specName || spec.specName.trim() === '') {
      return `第${index}个规格的规格名不能为空`;
    }

    if (!spec.specCover || spec.specCover.trim() === '') {
      return `第${index}个规格的规格封面不能为空`;
    }

    if (!spec.stockType) {
      return `第${index}个规格的库存类型不能为空`;
    }

    if (spec.stockType === 'limited' && (!spec.stockCount || spec.stockCount <= 0)) {
      return `第${index}个规格的库存量必须大于0`;
    }

    if (spec.consumePoints === undefined || spec.consumePoints === null || spec.consumePoints < 0) {
      return `第${index}个规格的消耗积分不能为空且不能小于0`;
    }

    if (spec.price === undefined || spec.price === null || spec.price <= 0) {
      return `第${index}个规格的商品价格必须大于0`;
    }
  }

  return null;
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    // 校验规格列表
    const specs = unref(productSpecs);
    const validationError = validateProductSpecs(specs);
    if (validationError) {
      await nextTick(() => {
        activeTab.value = 'specs';
      });
      throw new Error(validationError);
    }

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        productPublicityImg: values.productPublicityImg
          ? join(values.productPublicityImg, ',')
          : '',
        productSpecs: specs,
      },
      isUpdate: unref(isUpdate),
    });
  } catch (error) {
    // 如果是规格校验错误，确保切换到规格信息标签页
    if (error.message && error.message.includes('规格')) {
      await nextTick(() => {
        activeTab.value = 'specs';
      });
    }
    throw error;
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.product-info-modal {
  :global {
    .ant-modal-body {
      max-height: 70vh;
      overflow: auto;
      padding: 16px;
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }

    .ant-tabs-tabpane {
      outline: none;
    }
  }
}
</style>
