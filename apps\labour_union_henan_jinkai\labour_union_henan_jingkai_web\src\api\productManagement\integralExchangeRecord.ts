import { openHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findList = '/findVoList',
  view = '/getCurrentExchangeDetails',
  saveOrUpdate = '/',
  userInfoSearch = '/userInfoSearch',
  productSearch = '/productSearch',
}

function getApi(url?: string) {
  if (!url) {
    return '/integralProductExchangeRecord';
  }
  return '/integralProductExchangeRecord' + url;
}

// 不分页查询所有用户
export const userInfoSearch = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.userInfoSearch), params },
    {
      isTransformResponse: false,
    }
  );
};
// 不分页查询所有用户
export const productSearch = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.productSearch), params },
    {
      isTransformResponse: false,
    }
  );
};

//列表
export const list = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//订单列表
export const findOrderVoList = params => {
  return openHttp.get<BasicResponse>(
    { url: '/customOrder/findOrderVoList', params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return openHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
