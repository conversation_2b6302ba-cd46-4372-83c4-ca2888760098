import { defineApplicationConfig } from '@monorepo-yysz/vite-config';
import { generateModifyVars } from './build/modifyVars';

export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      include: [
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
        'lottie-web',
        'sortablejs',
        'cropperjs',
      ],
    },
    build: {
      outDir: 'hnjkdmin',
    },
    server: {
      host: true,
      port: 5200,
      proxy: {
        '/basic-api': {
          target: 'http://**************:33000',
          // target: 'https://ncadmin.nczgh.com/basic-api/',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/apisse': {
          target: 'http://**************:33000',
          // target: 'http://**************:33000',
          // target: 'https://ncadmin.nczgh.com/basic-api/',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/apisse`), ''),
          // only https
          // secure: false
        },
        '/upload': {
          target: 'http://**************:33000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/upload`), ''),
        },
        '/map-api': {
          target: 'https://api.map.baidu.com',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/map-api`), ''),
        },
        '/api-ali': {
          target: 'http://**************:33000',
          //https://ncadmin.nczgh.com/api-ali/
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/api-ali`), ''),
          bypass(req, res, options) {
            console.log(options.target, 'VITE_ALI_API');
          },
        },
      },
      open: false, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          modifyVars: generateModifyVars(),
        },
      },
    },
  },
});
