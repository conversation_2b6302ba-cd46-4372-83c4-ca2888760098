import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '/@/utils/http/axios';

enum API {
  findList = '/workIntegralProductFindVoList',
  view = '/getProductInfoByProductId',
  saveApi = '/addIntegralProductInfo',
  updateApi = '/updateIntegralProductInfo',
  enableOrDisableProduct = '/enableOrDisableProduct',
  getProductPriceInfoByProductId = '/getProductPriceInfoByProductId',
}

function getApi(url?: string) {
  if (!url) {
    return '/customProductInfo';
  }
  return '/customProductInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 上架/下架积分商品
export const enableOrDisableProduct = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.enableOrDisableProduct),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 通过商品id,获取商品的所有规格信息
export const getProductPriceInfoByProductId = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(API.getProductPriceInfoByProductId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
